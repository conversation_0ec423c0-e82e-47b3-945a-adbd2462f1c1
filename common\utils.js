import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { useLoginStore } from '@/store/login.js'

dayjs.extend(utc)
export const dayjsFn = (date) => dayjs(date)
/**
 * 获取节点信息
 */
export function $uGetRect(selector, all) {
  return new Promise((resolve) => {
    uni.createSelectorQuery()
      .in(this)[all ? 'selectAll' : 'select'](selector)
      .boundingClientRect((rect) => {
        if (all && Array.isArray(rect) && rect.length) {
          resolve(rect)
        }
        if (!all && rect) {
          resolve(rect)
        }
      })
      .exec()
  })
}

/**
 * 十进制转二进制
 */
export function decimalToBinaryReverseArray(num) {
  let arr = new Array(16).fill('0', 0, 16)
  let result = []
  let binary = num.toString(2)
  result = binary.split('')
  result.reverse()

  return arr.map((item, index) => {
    return result[index] ? result[index] : item
  })
}

/**
 * 导出下载文件
 */
export function handleExport(data, fileName, suffix = '.xlsx') {
  let blob = new Blob([data]);
  let url = URL.createObjectURL(blob);
  // 重命名文件名称
  let link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${fileName}${suffix}`
  );
  link.click();
}

/**
 * 权限标识
 */
export const isShowPerm = (value) => {
  const userStore = useLoginStore()
  const all_permission = "*:*:*";
  const permissions = userStore && uni.cache.getItem('permissions')

  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value

    const hasPermissions = permissions.some(permission => {
      return all_permission === permission || permissionFlag.includes(permission)
    })

    // if (!hasPermissions) {
    //   el.parentNode && el.parentNode.removeChild(el)
    // }
    
    return hasPermissions
  }
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  if (value && value instanceof Array && value.length > 0) {
    const userStore = useLoginStore()
    const roles = userStore && uni.cache.getItem('roles')
    const permissionRoles = value
    const super_admin = "admin";

    const hasRole = roles.some(role => {
      return super_admin === role || permissionRoles.includes(role)
    })

    if (!hasRole) {
      return false
    }
    return true
  } else {
    console.error(`need roles! Like checkRole="['admin','editor']"`)
    return false
  }
}

// 获取某月的所有日期
export function getAllDatesInMonth(year, month) {
  let dates = [];
  const startDate = dayjs(new Date(year, month - 1, 1)); // 月份是从0开始的
  const endDate = startDate.endOf('month');
 
  let currentDate = startDate;
  while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
    dates.push(currentDate.format('YYYY-MM-DD'));
    currentDate = currentDate.add(1, 'day');
  }
 
  return dates;
}
 
// 获取某年的所有日期
export function getAllDatesInYear(year) {
  let dates = [];
  for (let month = 1; month <= 12; month++) {
    dates = dates.concat(getAllDatesInMonth(year, month));
  }
  return dates;
}

// kb转mb
export function formatSizeUnits(kb) {
    let units = ['KB', 'MB', 'GB', 'TB', 'PB'];
    let unitIndex = 0;

    while (kb >= 1024 && unitIndex < units.length - 1) {
        kb /= 1024;
        unitIndex++;
    }

    return `${kb.toFixed(2)} ${units[unitIndex]}`;
}

// 判断路由是否存在
export function isShowRoute(path, routes) {
  let result = false
  const mapRoutes = (list) => {
    list.forEach(item => {
      if (!result) {
        if (item.path !== path) {
          if (item.children) mapRoutes(item.children)
        } else {
          result = true
        }
      }
    })
  }
  mapRoutes(routes)
  return result
}

// 计算设备SOC平均值
export const calculateAverageOfAverages = (data, field = 'bms_4022') => {
  if (!data.length) return 0;

  // 1. 按 ac 分组
  const groups = {};
  data.forEach((item) => {
    const key = item.ac;
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
  });

  // 2. 计算每个设备的平均值
  const deviceAverages = Object.keys(groups).map((ac) => {
    const values = groups[ac].map((item) => item[field]);
    const sum = values.reduce((a, b) => a + b, 0);
    return sum / values.length; // 每个设备的平均值
  });

  // 3. 计算所有设备平均值的平均值
  const totalAvg = deviceAverages.reduce((a, b) => a + b, 0) / deviceAverages.length;

  return totalAvg.toFixed(2);
};