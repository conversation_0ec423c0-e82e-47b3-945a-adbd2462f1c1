<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ bms[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="isExpand ? bmsValue: []">
      <u-collapse-item v-for="(bmsItem, index) in bms" :key="bmsItem.dc" :name="bmsItem.dc">
        <template #title>
          <view class="info-ti">{{ bmsItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('BMS通信状态') }}</view>
          <view v-if="getStatus(index) == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus(index) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池告警') }}</view>
          <view v-if="bmsItem['bms_4000']">{{ bmsItem['bms_4000'] == '1' ? $t('告警') :
                    $t('正常') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池故障') }}</view>
          <view v-if="bmsItem['bms_4001']">{{ bmsItem['bms_4001'] == '1' ? $t('故障') :
                    $t('正常') }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">SOC</view>
          <view class="flex">
            <up-line-progress :percentage="bmsItem['bms_4022']" :showText="false" height="30"
              :activeColor="otherColor.primaryColor" class="u-m-r-10" />{{ bmsItem['bms_4022'] }}<span
              class="item-unit">%</span>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">SOH</view>
          <view class="flex">
            <up-line-progress :percentage="bmsItem['bms_4023']" :showText="false" height="30"
              class="u-m-r-10" />{{ bmsItem['bms_4023'] }}<span class="item-unit">%</span>
          </view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池电压') }}</view>
          <view>{{ bmsItem['bms_4020'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4083']">
          <view class="color-grey">{{ $t('充放次数') }}</view>
          <view>{{ bmsItem['bms_4083'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电池电流') }}</view>
          <view>{{ bmsItem['bms_4021'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4036']">
          <view class="color-grey">{{ $t('电池可充电量') }}</view>
          <view>{{ bmsItem['bms_4036'] }}<span class="item-unit">kWh</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4037']">
          <view class="color-grey">{{ $t('电池可放电量') }}</view>
          <view>{{ bmsItem['bms_4037'] }}<span class="item-unit">kWh</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('充电限制电流') }}</view>
          <view>{{ bmsItem['bms_4030'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('放电限制电流') }}</view>
          <view>{{ bmsItem['bms_4031'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('最小单体电压') }}</view>
          <view>{{ bmsItem['bms_4025'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('最大单体电压') }}</view>
          <view>{{ bmsItem['bms_4024'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4026']">
          <view class="color-grey">{{ $t('平均单体电压') }}</view>
          <view v-if="bmsItem['bms_4026']">{{ bmsItem['bms_4026'] }}<span class="item-unit">V</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('最高单体温度') }}</view>
          <view>{{ bmsItem['bms_4027'] }}<span class="item-unit">℃</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('最低单体温度') }}</view>
          <view>{{ bmsItem['bms_4028'] }}<span class="item-unit">℃</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4029']">
          <view class="color-grey">{{ $t('平均单体温度') }}</view>
          <view v-if="bmsItem['bms_4029']">{{ bmsItem['bms_4029'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4041']">
          <view class="color-grey">{{ $t('最低单体电压位置') }}</view>
          <view v-if="bmsItem['bms_4041']">{{ bmsItem['bms_4041'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4043']">
          <view class="color-grey">{{ $t('最高单体电压位置') }}</view>
          <view v-if="bmsItem['bms_4043']">{{ bmsItem['bms_4043'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4054']">
          <view class="color-grey">{{ $t('电芯压差') }}</view>
          <view v-if="bmsItem['bms_4054']">{{ bmsItem['bms_4054'] }}<span class="item-unit">mV</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4045']">
          <view class="color-grey">{{ $t('最高单体温度位置') }}</view>
          <view v-if="bmsItem['bms_4045']">{{ bmsItem['bms_4045'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4047']">
          <view class="color-grey">{{ $t('最低单体温度位置') }}</view>
          <view v-if="bmsItem['bms_4047']">{{ bmsItem['bms_4047'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4053']">
          <view class="color-grey">{{ $t('电芯温差') }}</view>
          <view v-if="bmsItem['bms_4053']">{{ bmsItem['bms_4053'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4038'] && isShowV75019(index)">
          <view class="color-grey">{{ $t('环境温度(电池仓)') }}</view>
          <view v-if="bmsItem['bms_4038']">{{ bmsItem['bms_4038'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4055'] && isShowV75019(index)">
          <view class="color-grey">{{ $t('环境湿度(电池仓)') }}</view>
          <view v-if="bmsItem['bms_4055']">{{ bmsItem['bms_4055'] }}<span class="item-unit">%</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4039']">
          <view class="color-grey">{{ $t('绝缘阻抗值') }}</view>
          <view v-if="bmsItem['bms_4039']">{{ bmsItem['bms_4039'] }}<span class="item-unit">kΩ</span></view>
          <view v-else>--</view>
        </view>
        <!-- 电池空调状态 -->
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('整机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 0) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('内风机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 1) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('外风机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 2) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('压缩机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 3) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('电加热状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 4) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('应急风机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 5) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4058']">
          <view class="color-grey">{{ $t('空调开关机状态') }}</view>
          <view v-if="bmsItem['bms_4058']">{{ get4058(bmsItem['bms_4058'], 6) }}</view>
          <view v-else>--</view>
        </view>
        <!-- 出水温度 -->
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4059']">
          <view class="color-grey">{{ $t('出水温度') }}</view>
          <view v-if="bmsItem['bms_4059']">{{ bmsItem['bms_4059'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4060']">
          <view class="color-grey">{{ $t('进水温度') }}</view>
          <view v-if="bmsItem['bms_4060']">{{ bmsItem['bms_4060'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4061']">
          <view class="color-grey">{{ $t('出水压力') }}</view>
          <view v-if="bmsItem['bms_4061']">{{ bmsItem['bms_4061'] }}<span class="item-unit">Bar</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4062']">
          <view class="color-grey">{{ $t('进水压力') }}</view>
          <view v-if="bmsItem['bms_4062']">{{ bmsItem['bms_4062'] }}<span class="item-unit">Bar</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4063']">
          <view class="color-grey">{{ $t('空调状态') }}</view>
          <view v-if="bmsItem['bms_4063']">{{ get4063(bmsItem['bms_4063']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4064']">
          <view class="color-grey">{{ $t('空调模式') }}</view>
          <view v-if="bmsItem['bms_4064']">{{ get4064(bmsItem['bms_4064']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4003'] && checkRole(['admin'])">
          <view class="color-grey">{{ $t('BMS类型') }}</view>
          <view v-if="bmsItem['bms_4003']">{{ getBmsTypeFn(bmsItem['bms_4003']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="bmsItem['bms_4082']">
          <view class="color-grey">{{ $t('版本号') }}</view>
          <view v-if="bmsItem['bms_4082']">{{ bmsItem['bms_4082'] }}</view>
          <view v-else>--</view>
        </view>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    get4058
  } from '@/common/parseBinaryToText.js'
  import otherColor from '../../../common/other.module.scss'
  import { isGroupFn } from '@/hook/useDeviceType.js'
  import { onShow } from '@dcloudio/uni-app'
  import {
    checkRole
  } from '@/common/utils.js'
  
  const isExpand = defineModel('isExpand', {
    default: true
  })
  const props = defineProps({
    type: String
  })

  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()

  const baseInfo = computed(() => monitorStore.baseInfo)
  const bmsValue = ref([])
  const bms = computed(() => {
    let data = null
    if (props.type == 'bms') {
      data = monitorStore.pcs_bms
    } else {
      data = monitorStore.pcs_bmsBau
    }
    bmsValue.value = data.map(item => item.dc)
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    data.forEach(item => {
      if (isGroup) {
        item.name = props.type == 'bms'? `${item.label}_${parseInt(item.dc) - 161000 + 1}#BMS`: `${item.label}_${parseInt(item.dc) - 241000 + 1}#BMS-BAU`
      } else {
        item.name = props.type == 'bms'? `${parseInt(item.dc) - 161000 + 1}#BMS`: `${parseInt(item.dc) - 241000 + 1}#BMS-BAU`
      }
    })
    return data
  })
  const getStatus = computed(() => {
    return (index) => {
      if (bms.value[index].isAnalysis == 0) {
        return bms.value.onLineState == '在线' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else if (bms.value[index].isAnalysis == 1) {
        return bms.value[index]['bms_4002'] == '1' ? uni.$i18n().t('故障') : uni.$i18n().t('正常')
      } else {
        return '--'
      }
    }
  })
  const get4063 = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return uni.$t('关机')
        case '1':
          return uni.$t('开机')
        case '2':
          return uni.$t('故障')
      }
    }
  })
  const get4064 = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return '--'
        case '1':
          return uni.$t('内循环')
        case '2':
          return uni.$t('制冷')
        case '3':
          return uni.$t('制热')
      }
    }
  })
  const getBmsTypeFn = computed(() => {
    return (type) => {
      switch(type) {
        case '0':
          return uni.$t('宁德')
        case '1':
          return uni.$t('协能')
        case '2':
          return uni.$t('高泰昊能')
        case '3':
          return uni.$t('华塑')
        case '4':
          return uni.$t('高特')
        case '5':
          return uni.$t('华思')
        case '6':
          return uni.$t('小鸟')
        case '7':
          return uni.$t('山东威马')
        case '9':
          return uni.$t('亿纬锂电')
        case '10':
          return uni.$t('力神')
        case '11':
          return uni.$t('帷幕-BCU')
        case '12':
          return uni.$t('宁德液冷')
        case '13':
          return uni.$t('三级宁德')
        case '14':
          return uni.$t('优旦')
        case '15':
          return uni.$t('欣旺达')
        case '16':
          return 'BLK'
        case '17':
          return uni.$t('沛城电子')
        case '18':
          return uni.$t('帷幕-BCU2')
        case '19':
          return uni.$t('群控能源GCE')
        case '20':
          return uni.$t('高特三级bms')
        case '21':
          return 'Qualtech power'
        case '23':
          return uni.$t('科工')
        default:
          return '--'
      }
    }
  })

  const isShowV75019 = computed(() => {
    return (index) => {
      let type = monitorStore.routeQuery.type
      let isGroup = isGroupFn(type)
      let control = isGroup ? monitorStore.groupControl: monitorStore.control
      let versionStart = control[index]?.jk_1000?.split('V')[1].split('.')[0]
      let versionTwo = control[index]?.jk_1000?.split('V')[1].split('.')[1]
      let versionThere = control[index]?.jk_1000?.split('V')[1].split('.')[2]
      if (versionStart == 7)
        if (versionTwo == 5019) return false
      return true
    }
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }
  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-line) {
    border: none !important;
    border-bottom: 1px solid $monitor-data-border-color !important;
  }

  :deep(.u-line:first-child) {
    border-top: none !important;
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }

  :deep(.u-line-progress) {
    border-radius: 0px !important;
  }
</style>
