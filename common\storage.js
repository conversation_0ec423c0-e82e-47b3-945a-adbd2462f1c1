class Cache {
  
  constructor(sync = true) {
    this.sync = sync
  }
  
  setItem(key, value, callBack = () => {}) {
    this.sync ? uni.setStorageSync(key, JSON.stringify(value)): uni.setStorage({
      key,
      data: JSON.stringify(value)
    })
  }
  
  getItem(key) {
    return this.sync ? uni.getStorageSync(key) ? JSON.parse(uni.getStorageSync(key)): '': JSON.parse(uni.getStorage({
      key
    }))
  }
  
  remove(key) {
    this.sync ? uni.removeStorageSync(key): uni.removeStorage({
      key
    })
  }
}

export const cache = new Cache()