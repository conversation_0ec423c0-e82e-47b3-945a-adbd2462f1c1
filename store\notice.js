import {
  defineStore
} from 'pinia';
import {
  ref
} from 'vue'
import {
  getUnreadNoticeCount,
  readNotice
} from '@/api/notice.js'

export const useNoticeStore = defineStore('notice', () => {
  // 登录
  const unreadCount = ref(0)
  const getUnreadNoticeCountFn = async (index) => {
    if (index) uni.cache.setItem('unreadTabbarIndex', index)
    const res = await getUnreadNoticeCount()
    if (res.data == 0) {
      uni.hideTabBarRedDot({
        index: uni.cache.getItem('unreadTabbarIndex')
      })
    } else {
      uni.showTabBarRedDot({
        index: uni.cache.getItem('unreadTabbarIndex')
      })
    }
    unreadCount.value = res.data
  }

  const readNoticeFn = async (ids) => {
    await readNotice(ids)
  }

  return {
    unreadCount,
    getUnreadNoticeCountFn,
    readNoticeFn
  };
});