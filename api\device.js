// 获取设备列表
export const deviceList = (queryInfo) => uni.$u.http.get('/system/device/list', {
  params: queryInfo
})

// 新增设备
export const addDevice = (data) => uni.$u.http.post('/system/device', data)

// 修改设备
export const editDevice = (data) => uni.$u.http.put('/system/device', data)

// 获取设备详情
export const getDeviceInfo = (queryInfo) => uni.$u.http.get(`/system/device/${queryInfo.deviceId}`)

// 删除设备
export const deleteDevice = (queryInfo) => uni.$u.http.delete(`/system/device/${queryInfo.deviceIds}`)

// 查询未绑定的设备
export const getBandingList = (queryInfo) => uni.$u.http.get('/system/device/bindingList', {
  params: queryInfo
})

// 恢复出厂设备
export const recover = (queryInfo) => uni.$u.http.get(`/system/deviceMonitoring/clearData/${queryInfo.deviceSerialNumber}/${queryInfo.timeZone}/${queryInfo.deviceType}`)

// 分配设备 项目与设备一起分配
export const allotDevice = (data) => uni.$u.http.post('/system/deviceMonitoring/allotDevice', data)
