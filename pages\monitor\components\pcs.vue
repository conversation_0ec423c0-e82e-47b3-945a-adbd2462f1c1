<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="isExpand ? pcsValue: []">
      <u-collapse-item v-for="(acItem, index) in ac" :key="acItem.dc" :name="acItem.dc">
        <template #title>
          <view class="info-ti">{{ acItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('并离网状态') }}</view>
          <view>{{ parse.get2015Bit4(acItem['ac_2015']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('工作模式') }}</view>
          <view>{{ get2010Fn(acItem['ac_2010']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('充放电') }}</view>
          <view>{{ parse.get2015Bit5(acItem['ac_2015']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('运行状态') }}</view>
          <view>{{ parse.get2015Bit6(acItem['ac_2015']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('故障状态') }}</view>
          <view>{{ parse.get2015Bit7(acItem['ac_2015']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通讯状态') }}</view>
          <view v-if="getStatus('ac', index, 'ac_2000') == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus('ac', index, 'ac_2000') }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('直流电压') }}</view>
          <view>{{ acItem['ac_2039'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('直流电流') }}</view>
          <view>{{ acItem['ac_2040'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('直流功率') }}</view>
          <view>{{ acItem['ac_2041'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('线电压Vab') }}</view>
          <view>{{ acItem['ac_2042'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('线电压Vbc') }}</view>
          <view>{{ acItem['ac_2043'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('线电压Vca') }}</view>
          <view>{{ acItem['ac_2044'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('A相电流') }}</view>
          <view>{{ acItem['ac_2045'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('B相电流') }}</view>
          <view>{{ acItem['ac_2046'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('C相电流') }}</view>
          <view>{{ acItem['ac_2047'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('有功功率') }}</view>
          <view>{{ acItem['ac_2049'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('无功功率') }}</view>
          <view>{{ acItem['ac_2050'] }}<span class="item-unit">kVar</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('视在功率') }}</view>
          <view>{{ acItem['ac_2061'] }}<span class="item-unit">kVA</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('频率') }}</view>
          <view>{{ acItem['ac_2048'] }}Hz</view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="!commonRole">
          <view class="color-grey">{{ $t('igbt温度') }}</view>
          <view>{{ acItem['ac_2056'] }}<span class="item-unit">℃</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('功率因数') }}</view>
          <view>{{ acItem['ac_2051'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('正极对地阻抗值') }}</view>
          <view v-if="acItem['ac_2057']">{{ acItem['ac_2057'] }}<span class="item-unit">kΩ</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('负极对地阻抗值') }}</view>
          <view v-if="acItem['ac_2058']">{{ acItem['ac_2058'] }}<span class="item-unit">kΩ</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('版本号') }}</view>
          <view v-if="acItem['ac_2002']">{{ acItem['ac_2002'] }}</view>
          <view v-else>--</view>
        </view>
      </u-collapse-item>
      <u-collapse-item v-for="(dcItem, index) in dc" :key="dcItem.dc" :name="dcItem.dc">
        <template #title>
          <view class="info-ti">{{ dcItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('高压侧类型') }}</view>
          <view>{{ get3028Fn(dcItem['dc_3028']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('工作模式') }}</view>
          <view>{{ get3028Fn(dcItem['dc_3029']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('运行状态') }}</view>
          <view>{{ parse.get3004Bit6(dcItem['dc_3004']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('故障状态') }}</view>
          <view>{{ parse.get3004Bit7(dcItem['dc_3004']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('工作模式') }}</view>
          <view>{{ get3003Fn(dcItem['dc_3003']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通讯状态') }}</view>
          <view v-if="getStatus('dc', index, 'dc_3000') == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus('dc', index, 'dc_3000') }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧1路电压') }}</view>
          <view>{{ dcItem['dc_3012'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧1路电流') }}</view>
          <view>{{ dcItem['dc_3013'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧1路功率') }}</view>
          <view>{{ dcItem['dc_3014'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧2路电压') }}</view>
          <view>{{ dcItem['dc_3015'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧2路电流') }}</view>
          <view>{{ dcItem['dc_3016'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC低压侧2路功率') }}</view>
          <view>{{ dcItem['dc_3017'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC高压侧电压') }}</view>
          <view>{{ dcItem['dc_3018'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC高压侧电流') }}</view>
          <view>{{ dcItem['dc_3019'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('DC高压侧功率') }}</view>
          <view>{{ dcItem['dc_3020'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items" v-if="!commonRole">
          <view class="color-grey">{{ $t('igbt温度') }}</view>
          <view>{{ dcItem['dc_3025'] }}<span class="item-unit">℃</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('正母线电压') }}</view>
          <view>{{ dcItem['dc_3026'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('负母线电压') }}</view>
          <view>{{ dcItem['dc_3027'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('版本号') }}</view>
          <view>{{ dcItem['dc_3002'] }}</view>
        </view>
      </u-collapse-item>
      <u-collapse-item v-for="(stsItem, index) in sts" :key="stsItem.dc" :name="stsItem.dc">
        <template #title>
          <view class="info-ti">{{ stsItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('通信状态') }}</view>
          <view v-if="getStatus('sts', index, 'sts_3500') == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus('sts', index, 'sts_3500') }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('版本号') }}</view>
          <view>{{ stsItem['sts_3501'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('设备状态') }}</view>
          <view>{{ parse.get3002Bit0(stsItem['sts_3502']) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电压A(AB)') }}</view>
          <view>{{ stsItem['sts_3507'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电压B(BC)') }}</view>
          <view>{{ stsItem['sts_3508'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电压C(CA)') }}</view>
          <view>{{ stsItem['sts_3509'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电流A') }}</view>
          <view>{{ stsItem['sts_3511'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电流B') }}</view>
          <view>{{ stsItem['sts_3512'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网电流C') }}</view>
          <view>{{ stsItem['sts_3513'] }}<span class="item-unit">A</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网有功A') }}</view>
          <view>{{ stsItem['sts_3519'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网有功B') }}</view>
          <view>{{ stsItem['sts_3520'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网有功C') }}</view>
          <view>{{ stsItem['sts_3521'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网无功A') }}</view>
          <view>{{ stsItem['sts_3523'] }}<span class="item-unit">kVar</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网无功B') }}</view>
          <view>{{ stsItem['sts_3524'] }}<span class="item-unit">kVar</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网无功C') }}</view>
          <view>{{ stsItem['sts_3525'] }}<span class="item-unit">kVar</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网总有功') }}</view>
          <view>{{ stsItem['sts_3522'] }}<span class="item-unit">kW</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网总无功') }}</view>
          <view>{{ stsItem['sts_3526'] }}<span class="item-unit">kVar</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网总视在功率') }}</view>
          <view>{{ stsItem['sts_3534'] }}<span class="item-unit">kVA</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网视在功率A') }}</view>
          <view>{{ stsItem['sts_3531'] }}<span class="item-unit">kVA</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网视在功率B') }}</view>
          <view>{{ stsItem['sts_3532'] }}<span class="item-unit">kVA</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网视在功率C') }}</view>
          <view>{{ stsItem['sts_3533'] }}<span class="item-unit">kVA</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('负载电压A(AB)') }}</view>
          <view>{{ stsItem['sts_3515'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('负载电压B(BC)') }}</view>
          <view>{{ stsItem['sts_3516'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('负载电压C(CA)') }}</view>
          <view>{{ stsItem['sts_3517'] }}<span class="item-unit">V</span></view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('功率因数A') }}</view>
          <view>{{ stsItem['sts_3527'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('功率因数B') }}</view>
          <view>{{ stsItem['sts_3528'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('功率因数C') }}</view>
          <view>{{ stsItem['sts_3529'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('总功率因数') }}</view>
          <view>{{ stsItem['sts_3530'] }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('电网频率') }}</view>
          <view>{{ stsItem['sts_3518'] }}<span class="item-unit">Hz</span></view>
        </view>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import * as parse from '@/common/parseBinaryToText.js'
  import { isGroupFn } from '@/hook/useDeviceType.js'
  import { onShow } from '@dcloudio/uni-app'
  
  const isExpand = defineModel('isExpand', {
    default: true
  })
  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()

  const baseInfo = computed(() => monitorStore.baseInfo)
  const pcsValue = ref([])
  const ac = computed(() => {
    let data = monitorStore.pcs_ac
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    data.forEach(item => {
      pcsValue.value.push(item.dc)
      if (isGroup) {
        item.name = `${item.label}_${parseInt(item.dc) - 131000 + 1}#Monet-AC`
      } else {
        item.name = `${parseInt(item.dc) - 131000 + 1}#Monet-AC`
      }
    })
    return data
  })
  const dc = computed(() => {
    let data = monitorStore.pcs_dc
    let type = monitorStore.routeQuery.type
    let isGroup = type == 10000 || type == 10001 || type == 10002
    data.forEach(item => {
      pcsValue.value.push(item.dc)
      if (isGroup) {
        item.name = `${item.label}_${parseInt(item.dc) - 141000 + 1}#Monet-DC`
      } else {
        item.name = `${parseInt(item.dc) - 141000 + 1}#Monet-DC`
      }
    })
    return data
  })
  const sts = computed(() => {
    let data = monitorStore.pcs_sts
    let type = monitorStore.routeQuery.type
    let isGroup = type == 10000 || type == 10001 || type == 10002
    data.forEach(item => {
      pcsValue.value.push(item.dc)
      if (isGroup) {
        item.name = `${item.label}_${parseInt(item.dc) - 151000 + 1}#Monet-STS`
      } else {
        item.name = `${parseInt(item.dc) - 151000 + 1}#Monet-STS`
      }
    })
    return data
  })
  const sdt = computed(() => {
    if (ac.value.length) {
      return ac.value[0].sdt
    } else if (dc.value.length) {
      return dc.value[0].sdt
    } else if (sts.value.length) {
      return sts.value[0].sdt
    }
  })
  const get2010Fn = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return 'PQ'
        case '1':
          return 'MPPT'
        case '2':
          return 'CV'
        case '3':
          return 'VSG'
        case '4':
          return 'VF'
      }
    }
  })
  const get3003Fn = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return 'CP/CC'
        case '1':
          return 'MPPT'
        case '2':
          return 'CV'
      }
    }
  })
  const get3028Fn = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return uni.$i18n().t('锂电池')
        case '1':
          return uni.$i18n().t('铅酸电池')
        case '2':
          return uni.$i18n().t('光伏')
        case '3':
          return uni.$i18n().t('直流母线')
        case '4':
          return uni.$i18n().t('直流源')
      }
    }
  })
  const commonRole = computed(() => {
    let roles = userStore.roles
    return roles.findIndex(item => item == 'common') !== -1
  })
  const getStatus = computed(() => {
    return (type, index, item) => {
      let data = null
      if (type == 'ac') {
        data = ac.value
      } else if (type == 'dc') {
        data = dc.value
      } else if (type == 'sts') {
        data = sts.value
      }
      if (data[index].isAnalysis == 0) {
        return data[index].onLineState == '在线' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else if (data[index].isAnalysis == 1) {
        return data[index][item] == '1' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else {
        return '--'
      }
    }
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }
  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-size: 14px;
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>