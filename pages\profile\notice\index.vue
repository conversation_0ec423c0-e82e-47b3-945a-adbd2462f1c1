<template>
  <view class="notice-list">
    <z-paging ref="paging" v-model="noticeData" show-refresher-update-time refresher-update-time-key="noticeList"
      @query="queryList" hide-empty-view>
      <template #top>
        <u-navbar :title="$t('公告通知')" leftIconSize="20px" :autoBack="true" :bgColor="otherColor.bgColor" :placeholder="true" class="tabbar" :titleStyle="{
          color: '#000'
        }">
          <template #right>
            <u-button type="primary" size="small" @click="handleNoticeItemClick(null, 'all')"
              v-if="hasUnreadNotices">{{ $t('全部已读') }}</u-button>
          </template>
        </u-navbar>
      </template>

      <!-- 公告列表 -->
      <view class="notice-item" v-for="item in noticeData" :key="item.noticeId" @click="handleNoticeItemClick(item)">
        <view class="notice-header flex justify-content align-items">
          <view class="notice-title flex align-items">
            <image src="../../../static/bell1.png" class="notice-icon u-m-r-10"></image>
            <view class="u-line-1">{{ item[getPropFn('title')] }}</view>
          </view>
          <view class="notice-dot" v-if="item.isRead == 0"></view>
        </view>

        <view class="notice-content">
          <view class="notice-summary" v-html="item[getPropFn('cont')]"></view>
          <view class="notice-meta flex justify-content align-items">
            <view class="notice-time">{{ item.createTime }}</view>
            <view class="notice-action flex align-items">
              <view>{{ $t('查看详情') }}</view>
              <u-icon name="arrow-right" class="u-m-l-10" size="20"></u-icon>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-if="!noticeData.length">
        <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无公告')">
        </u-empty>
      </view>
    </z-paging>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import {
    getUnreadNoticeList,
    readNotice
  } from '@/api/notice.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    useNoticeStore
  } from '@/store/notice.js'

  const {
    proxy
  } = getCurrentInstance()
  const noticeStore = useNoticeStore()

  const noticeData = ref([])
  const paging = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 计算是否有未读公告
  const hasUnreadNotices = computed(() => {
    return noticeData.value.some(item => !item.isRead)
  })

  /**
   * 查询公告列表
   */
  const noticeListFn1 = async ({
    pageNum,
    pageSize
  }) => {
    const res = await getUnreadNoticeList({
      pageNum,
      pageSize
    })
    paging.value.total = res.total
    return res.rows
  }
  const queryList = (pageNo, pageSize) => {
    const params = {
      pageNum: pageNo,
      pageSize
    }

    noticeListFn1({
      pageNum: pageNo,
      pageSize
    }).then(res => {
      // 将请求的结果数组传递给z-paging
      proxy.$refs.paging.completeByTotal(res, paging.value.total)
    }).catch(res => {
      // 如果请求失败写this.$refs.paging.complete(false);
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      proxy.$refs.paging.complete(false);
    })
  }

  /**
   * 点击公告项，跳转到详情页
   */
  const handleNoticeItemClick = (item, type) => {
    let data = null
    if (type == 'all') {
      data = noticeData.value.map(item => item.noticeIdRecipients)
    } else {
      data = [item.noticeIdRecipients]
    }
    // 如果是未读状态，先标记为已读
    if (!item.isRead) {
      noticeStore.readNoticeFn(data)
    }

    // 跳转到详情页
    uni.navigateTo({
      url: `/pages/profile/notice/detail?noticeId=${item.noticeId}`
    })
  }

  const getPropFn = (type) => {
    const lang = uni.cache.getItem('language')
    if (type == 'title') {
      switch (lang) {
        case 'zh':
          return 'noticeTitle'
        case 'en':
          return 'noticeTitleUs'
        case 'it':
          return 'noticeTitleIt'
        default:
          return 'noticeTitle'
      }
    } else {
      switch (lang) {
        case 'zh':
          return 'noticeContent'
        case 'en':
          return 'noticeContentUs'
        case 'it':
          return 'noticeContentIt'
        default:
          return 'noticeContent'
      }
    }
  }
</script>

<style scoped lang="scss">
  .notice-list {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .navbar-right {
      padding: 0 15rpx;
    }

    .notice-item {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 30rpx;
      margin: 20rpx 30rpx;

      .notice-header {
        margin-bottom: 20rpx;
        width: 100%;

        .notice-title {
          flex: 1;
          font-size: 14px;
          font-weight: 600;
          color: $uni-text-color;
          line-height: 1.4;
        }

        .notice-icon {
          width: 30rpx;
          height: 30rpx;
        }

        .notice-dot {
          width: 14rpx;
          height: 14rpx;
          background: red;
          border-radius: 50%;
        }
      }

      .notice-content {
        .notice-summary {
          font-size: 14px;
          color: $uni-text-color-grey;
          line-height: 1.5;
          margin-bottom: 30rpx;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
        }

        .notice-meta {
          border-top: 1px solid #ebebeb;
          padding-top: 30rpx;
          color: $uni-text-color-grey;
          font-size: 12px;

          .notice-actions {
            margin-left: 20rpx;
          }
        }
      }
    }

    .empty-container {
      background-color: #fff;
      margin: 0 30rpx;
      border-radius: 6px;
      padding: 30rpx 0;
      height: 70vh;
      padding-top: 200rpx;
    }
  }
</style>