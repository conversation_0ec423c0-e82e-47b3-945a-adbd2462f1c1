# zu-battery
## 组件支持uvue页面
```vue
			<!-- 基础用法 -->
			<zu-battery :battery="20"></zu-battery>
			<zu-battery :battery="40"></zu-battery>
			<zu-battery :battery="60"></zu-battery>
			<!-- 不显示百分比 -->
			<zu-battery :showPercent="false" :battery="60"></zu-battery>
			<!-- 修改字体颜色 -->
			<zu-battery :battery="60" color="red"></zu-battery>
			<!-- 修改宽度 -->
			<zu-battery :battery="60" width="30"></zu-battery>
			<!-- 修改高度 -->
			<zu-battery :battery="80" height="14"></zu-battery>
			<zu-battery :battery="80" height="10"></zu-battery>
			<!-- 修改色阶 -->
			<zu-battery :battery="20" :batteryValues="batteryValues" :batteryColors="batteryColors"></zu-battery>
			<zu-battery :battery="40" :batteryValues="batteryValues" :batteryColors="batteryColors"></zu-battery>
			<zu-battery :battery="60" :batteryValues="batteryValues" :batteryColors="batteryColors"></zu-battery>
```

```js


const batteryValues = [20, 40, 100]

const batteryColors = ['#ff0000', '#ffff00', '#4cd964']
```

# props

|  属性   | 说明  | 类型 | 默认值 |
|  ----  | ----  | ---- | ---- |
| battery  | 电量（0-100） | Number | 0 |
| color  | 百分比字体颜色 | String |  #1c1c1c |
| width  | 电池宽度(默认单位px, 可传rpx) | String | 22 |
| height  | 电池高度(默认单位px) 建议传入双数 | String | 12 |
| showPercent  | 是否显示百分比 | Boolean | false |
| batteryValues | 电量色阶(必须为升序) | Array<number> | [20, 40, 100] |
| batteryColors | 电量色阶(必须为升序) | Array<string> | ['#ff0000', '#ffff00', '#4cd964'] |