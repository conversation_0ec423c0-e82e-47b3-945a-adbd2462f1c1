<template>
  <view style="height: 100vh;padding: 20rpx 30rpx;overflow-y: auto;">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="setting1912" :label="$t('有功功率设置')" required>
          <u-number-box v-model="form.setting1912" :min="-100" :max="100" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1913" :label="$t('无功功率设置')" required>
          <u-number-box v-model="form.setting1913" :min="-100" :max="100" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1914" :label="$t('功率因数设置')" required>
          <u-number-box v-model="form.setting1914" :step="0.1" :min="-1" :max="1" decimal-length="3" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1915" :label="$t('并离网设置')" required>
          <u-radio-group v-model="form.setting1915" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('并网')" name="0" class="mr-20"></u-radio>
            <u-radio :label="$t('离网')" name="1"></u-radio>
          </u-radio-group>
        </u-form-item>
      </u-form>
    </view>
    <view
      style="margin-top: 20rpx;font-size: 12px;padding: 20rpx 30rpx;background-color: #fff;border-radius: 6px;color: #b7b7b7;">
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('下发状态') }}：
        </view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('有功功率设置') }}：
        </view>
        <view>{{ $t('在手动模式下，控制系统输入出有功功率，正为放电，负为充电。') }}（-100~100）%</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('无功功率设置') }}：
        </view>
        <view>{{ $t('控制系统输出无功功率正为容性，负为感性。') }}（-100~100）%</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('功率因数设置') }}：
        </view>
        <view>{{ $t('调节输出的有功功率和无功功率的比值。') }}（-1~1）</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('并离网设置') }}：
        </view>
        <view>{{ $t('在手动控制下，设置其系统是否并入电网。') }}</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx;border-radius: 6px;" @click="handleSendClick"
      :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey"
      style="width: 100%;display: flex; align-items: center;justify-content: center;" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx;margin-top: 1rpx;"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed,
    toRefs
  } from 'vue';
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    onShow,
    onLoad
  } from '@dcloudio/uni-app'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { paramLabelStyle, inputNumberStyle } from '@/constant'
  

  const paramStore = useParamStore()
  const {
    routeQuery,
    control,
    groupControl
  } = toRefs(useMonitorStore())
  const {
    proxy
  } = getCurrentInstance()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  const current = ref()
  onLoad((options) => {
    current.value = options.index
  })

  // 使用 reactive 创建响应式状态  
  const form = ref({
    setting1912: undefined,
    setting1913: undefined,
    setting1914: undefined,
    setting1915: "0",
    status: 0
  })
  const rules = ref({
    setting1915: {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    // setting1912: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
    // setting1913: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
    // setting1914: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
  })
  const formRef = ref(null);
  const handleSendClick = () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    if (formRef.value) {
      formRef.value.validate().then(async valid => {
        if (valid) {
          try {
            await paramStore.sendParamMACFn({
              setting1912: form.value.setting1912,
              setting1913: form.value.setting1913,
              setting1914: form.value.setting1914,
              setting1915: form.value.setting1915,
              ac: ac,
              id: form.value.id
            })
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发成功'))
            }, 20)
            getInfo()
          } catch (e) {
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发失败'))
            }, 20)
            getInfo()
          }
        }
      })
    }
  }

  const isSend = computed(() => {
    if (isGroup.value) {
      let ac = routeQuery.value.groupId[current.value]
      let value = groupControl.value.find(item => item.ac == ac)
      if (!Object.keys(value).length) return true
      return value['onLineState'] == '离线'
    } else {
      if (!Object.keys(control.value).length) return true
      return control.value['onLineState'] == '离线'
    }
  })

  const getInfo = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    const res = await paramStore.macInfoFn({
      ac
    })
    if (!res) return
    for (let key in res) {
     if (res[key] == null) res[key] = undefined
    }
    let data = JSON.parse(JSON.stringify(form.value))
    // if (res.status == 1 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发成功'))
    // } else if (res.status == 3 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发失败'))
    // }
    form.value = {
      ...res,
    }
  }
  onShow(() => {
    getInfo()
  })
</script>

<style scoped lang="scss">
  .send-status {
    display: flex;
    align-items: center;

    image {
      margin-right: 6rpx;
    }
  }

  .param-form {
    width: 100%;
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>