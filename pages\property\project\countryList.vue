<template>
  <view class="scheme-list">
    <u-navbar :title="navBarTitle" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;"
          @click="handleCconfirmClick">{{ $t('完成') }}</u-button>
      </template>
    </u-navbar>
    
    <view class="scheme-list-wrap u-p-t-10 u-p-b-10 u-m-b-20">
      <u-search v-model="searchValue" :clearabled="true" searchIconSize="30rpx" height="60rpx" :placeholder="placeholder" @search="handleSearchClick" @custom="handleSearchClick" :actionText="$t('搜索')"></u-search>
    </view>
    
    <view class="scheme-list-wrap" v-if="initData.type == 'country'">
      <view class="scheme-list-wrap-item flex" v-for="item in countryData" :key="item.value"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.value == initData.value }">
          {{ lang == 'zh-Hans' ? item.text: item.value }}
        </view>
        <view v-if="item.value == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view class="scheme-list-wrap" v-if="initData.type == 'time'">
      <view class="scheme-list-wrap-item flex" v-for="item in timeData" :key="item.id" @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.value }">
          {{ item.timeZoneAddress }}
        </view>
        <view v-if="item.id == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view class="scheme-list-wrap" v-if="initData.type == 'currency'">
      <view class="scheme-list-wrap-item flex" v-for="item in currencyData" :key="item.id"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.value }">
          {{ item.country }}-{{ item.currency }}
        </view>
        <view v-if="item.id == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <template v-if="initData.type == 'device'">
      <view class="scheme-list-wrap1" v-for="item in deviceData" :key="item.deviceId">
        <view class="scheme-list-wrap1-item flex" @click="handleItemClick(item)">
          <view class="scheme-list-wrap1-item-left flex" :class="{ 'actived': isActived(item.deviceId) }">
            <u-icon name="list" :color="isActived(item.deviceId) ? '#3c9cff': '#333'"></u-icon>
            <view class="u-line-1 ml-5">{{ item.deviceName }}</view>
          </view>
          <view v-if="isActived(item.deviceId)">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
        <view class="flex u-flex-between" @click="handleDetailsClick(item)">
          <view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('整机序列号') }}：{{ item.deviceSerialNumber }}</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('设备型号') }}：{{ item.deviceModel }}</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('额定功率') }}：{{ item.deviceRatedPower }} kW</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('设备类型') }}：{{ $t(getDeviceType(item.deviceType, true)) }}</view>
            </view>
          </view>
          <u-icon name="arrow-right" color="#333"></u-icon>
        </view>
      </view>
    </template>
    <view v-if="isShowEmpty" style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    useParamStore
  } from '@/store/param'
  import {
    countryList
  } from './world'
  import {
    timeList
  } from '@/api/time'
  import {
    allCurrency
  } from '@/api/currency'
  import {
    getBandingList
  } from '@/api/device'
  import {
    getDeviceType
  } from '@/hook/useDeviceType.js'
  
  const countryData = ref(countryList)
  const lang = uni.cache.getItem('language')
  const initData = ref()
  const navBarTitle = ref()
  const isShowEmpty = ref(false)
  onLoad((options) => {
    initData.value = {
      ...options
    }
    if (initData.value.type == 'time') {
      navBarTitle.value = uni.$t('选择时区')
      placeholder.value = uni.$t('请输入') + '8'
    } else if (initData.value.type == 'currency') {
      navBarTitle.value = uni.$t('选择货币')
      placeholder.value = uni.$t('请输入关键字')
    } else if (initData.value.type == 'country') {
      navBarTitle.value = uni.$t('选择国家')
      isShowEmpty.value = !countryData.value.length
      placeholder.value = uni.$t('请输入关键字')
    } else if (initData.value.type == 'device') {
      navBarTitle.value = uni.$t('选择设备')
      placeholder.value = uni.$t('请输入设备名称')
    }
  })
  onShow(() => {
    if (initData.value.type == 'time') {
      getTimeList()
    } else if (initData.value.type == 'currency') {
      getCurrentcyList()
    } else if (initData.value.type == 'device') {
      getDeviceList()
    }
  })
  const selectDeviceData = ref([])
  uni.$on('addBindDeviceClick', (data) => {
    if (!data) return 
    selectDeviceData.value = [...data]
  })
  onUnload(() => {
    uni.$off('addBindDeviceClick')
  })

  const selectData = ref()
  const handleItemClick = (item) => {
    if (initData.value.type == 'time') {
      initData.value.value = item.id
    } else if (initData.value.type == 'currency') {
      initData.value.value = item.id
    } else if (initData.value.type == 'country') {
      initData.value.value = item.value
    } else if (initData.value.type == 'device') {
      selectDeviceData.value.push(item)
    }
    selectData.value = item
  }
  const handleCconfirmClick = () => {
    setTimeout(() => {
      initData.value.type == 'device' ? uni.$emit('countryItemClick', selectDeviceData.value) : uni.$emit(
        'countryItemClick', selectData.value)
      uni.navigateBack({
        delta: 1
      })
    }, 300)
  }

  /**
   * 获取时区
   */
  const timeData = ref([])
  const getTimeList = async () => {
    const res = await timeList({
      pageNum: 1,
      pageSize: 100,
      timeZone: searchValue.value
    })
    timeData.value = res.rows
    isShowEmpty.value = !timeData.value.length
  }
  /**
   * 获取货币
   */
  const currencyData = ref([])
  const getCurrentcyList = async () => {
    const res = await allCurrency({
      currency: searchValue.value
    })
    currencyData.value = res.data
    isShowEmpty.value = !currencyData.value.length
  }
  /**
   * 获取设备
   */
  const deviceData = ref([])
  const getDeviceList = async () => {
    const res = await getBandingList({
      pageNum: 1,
      pageSize: 100,
      deviceName: searchValue.value
    })
    deviceData.value = res.rows
    isShowEmpty.value = !deviceData.value.length
  }
  const isActived = computed(() => {
    return (id) => {
      return selectDeviceData.value.findIndex(item => item.deviceId == id) !== -1 || initData.value.value?.split(
        ',').findIndex(item => item == id) !== -1
    }
  })
  const handleDetailsClick = (item) => {
    uni.navigateTo({
      url: `/pages/property/device/device-detail?id=${item.deviceId}&isShow=false`
    })
  }
  
  /**
   * 搜索
   */
  const searchValue = ref()
  const placeholder = ref(uni.$t('请输入关键字'))
  const handleSearchClick = () => {
    if (initData.value.type == 'time') {
      getTimeList()
    } else if (initData.value.type == 'currency') {
      getCurrentcyList()
    } else if (initData.value.type == 'country') {
      if (!searchValue.value) return countryData.value = countryList
      countryData.value = countryList.filter(item => {
        if (lang == 'zh-Hans') {
          return item.text.indexOf(searchValue.value) != -1
        } else {
          return item.value.indexOf(searchValue.value) != -1
        }
      })
      isShowEmpty.value = !countryData.value.length
    } else if (initData.value.type == 'device') {
      getDeviceList()
    }
  }
  const getPropFn = () => {
    const lang = uni.cache.getItem('language')
    switch (lang) {
      case 'zh':
        return 'timeZoneAddress'
      case 'en':
        return 'timeZoneAddressUs'
      case 'it':
        return 'timeZoneAddressIt'
      default:
        return 'timeZoneAddress'
    }
  }
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item1 {
        padding: 10rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>