export const login = (data) => uni.$u.http.post('/login', data, { custom: { auth: false, catch: true } })

export const getInfo = () => uni.$u.http.get('/getInfo')

export const logout = () => uni.$u.http.post('/logout')

export const getUserProfile = () => uni.$u.http.get('/system/user/profile', {
  custom: {
      loading: false
    }
})

// 修改
export const updateUserPwd = (data) => uni.$u.http.put('/system/user/profile/updatePwd', data)

// 修改用户个人信息
export const updateUserProfile = (data) => uni.$u.http.put('/system/user/profile', data)

// 获取路由
export const getRouters = () => uni.$u.http.get('/getRouters')


// 查询用户列表
export const listUser = (queryInfo) => uni.$u.http.get('/system/user/list', {
  params: queryInfo
})