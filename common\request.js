import {
  useLoginStore
} from '@/store/login.js'
import otherColor from './other.module.scss'
import { languageArr } from '@/locale/index.js'

// 是否正在刷新token的标记
let isRefreshing = false
// 重试请求队列
let requests = []

export const initService = () => {
  const serviceArr = JSON.parse(import.meta.env.VITE_SERVICE_ARR).map(item => {
    if (item.value == 'zh' || item.value == 'en' || item.value == 'test') {
      item.label = uni.$t(item.label)
    }
    return item
  })
  let cacheService = uni.cache.getItem('service')
  serviceArr.forEach((item, index, self) => {
    if (cacheService) {
      if (item.value == cacheService) {
        uni.cache.setItem('service', item.value)
        config.baseURL = item.service
      }
    } else {
      uni.cache.setItem('service', self[0].value)
      config.baseURL = self[0].service
    }
  })
  initRequest(null, 2)
}

export const config = {
  baseURL: 'http://47.115.47.107:8080',
  custom: {
    auth: true,
    loading: true
  }, // 全局自定义参数默认值
}
const errorCode = {
  '401': '认证失败，无法访问系统资源',
  '403': '当前操作没有权限',
  '404': '访问资源不存在',
  'default': '系统未知错误，请反馈给管理员'
}
// 是否显示重新登录
export let isRelogin = {
  show: false
}

const requestInterceptors = (vm) => {
  /**
   * 请求拦截
   * @param {Object} http
   */
  uni.$u.http.interceptors.request.use((config) => { // 可使用async await 做异步操作
    // debugger
    // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
    config.data = config.data || {}
    // 可以在此通过vm引用vuex中的变量，具体值在vm.$store.state中
    const language = uni.cache.getItem('language') ? uni.cache.getItem('language') : 'en'
    config.header.Language = config.header.Language || languageArr.find(item => item.value == language).api
    const token = uni.cache.getItem('token') ? uni.cache.getItem('token') : ''
    if (config.custom.auth) {
      config.header.Authorization = 'Bearer ' + token
    }
    if (config.custom.loading) {
      uni.showLoading()
    }
    // // 演示
    // if (!token) { // 如果token不存在，return Promise.reject(config) 会取消本次请求
    // 	return Promise.reject(config)
    // }
    return config
  }, (config) => // 可使用async await 做异步操作
    Promise.reject(config))
}
const responseInterceptors = (vm) => {
  /**
   * 响应拦截
   * @param {Object} http 
   */
  uni.$u.http.interceptors.response.use(async (response) => {
    /* 对响应成功做点什么 可使用async await 做异步操作*/
    const data = response.data
    // 自定义参数
    const custom = response.config?.custom
    // 未设置状态码则默认成功状态
    const code = data?.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || data?.msg || errorCode['default']
    // 二进制数据则直接返回
    if (response.config?.responseType === 'blob' || response.config?.responseType === 'arraybuffer') {
      return data
    }
    // console.log(response)
    if (code === 401) {
      uni.hideLoading()
      const {
        LogOut,
        Login
      } = useLoginStore()
      const remberValueS = uni.cache.getItem('rememberValue')
      if (!isRefreshing) {
        if (remberValueS) {
          // 正在刷新
          isRefreshing = true
          try {
            const res = await Login({
              username: uni.cache.getItem('username'),
              password: uni.cache.getItem('password')
            })
            requests.forEach(async ({
              fn,
              resolve
            }) => {
              // 逐个按请求队列顺序重新发起请求
              const res = await fn()
              resolve(res)
            })
            requests = [] // 清空请求队列
            isRefreshing = false
            return uni.$u.http.request(response.config)
          } catch (e) {
            uni.reLaunch({
              url: '/pages/common/login'
            })
          }
        } else {
          isRefreshing = false
          if (!isRelogin.show) {
            isRelogin.show = true;
            uni.showModal({
              content: uni.$t('登录状态已过期，您可以继续留在该页面，或者重新登录'),
              title: uni.$t('系统提示'),
              confirmText: uni.$t('重新登录'),
              cancelColor: otherColor.mainColor,
              confirmColor: otherColor.primaryColor,
              showCancel: true,
              success: function (res) {
                if (res.confirm) {
                  isRelogin.show = false;
                  LogOut().then(() => {
                    uni.reLaunch({
                      url: '/pages/common/login'
                    })
                  })
                } else if (res.cancel) {
                  isRelogin.show = false;
                }
              }
            });
          }
          return Promise.reject(uni.$t('无效的会话，或者会话已过期，请重新登录。'))
        }
      } else {
        // 同时并发出现的请求 新的token没回来之前 先用promise 存入等待队列中
        return new Promise((resolve) => {
          const fn = () => Promise.resolve(uni.$u.http.request(response.config))
          requests.push({
            fn,
            resolve
          })
        })
      }

    } else if (code !== 200) { // 服务端返回的状态码不等于200，则reject()
      // 如果没有显式定义custom的toast参数为false的话，默认对报错进行toast弹出提示
      if (custom.toast !== false) {
        uni.$u.toast(msg)
      }
      // 如果需要catch返回，则进行reject
      if (custom?.catch) {
        uni.hideLoading()
        return Promise.reject(data)
      } else {
        uni.hideLoading()
        // 否则返回一个pending中的promise
        return new Promise(() => { })
      }
    }
    uni.hideLoading()
    return data || {}
  }, (response) => {
    // let { message } = error;
    // if (message == "Network Error") {
    // 	message = i18n.t('axios.error');
    // } else if (message.includes("timeout")) {
    // 	message = i18n.t('axios.timeout');
    // } else if (message.includes("Request failed with status code")) {
    // 	message = i18n.t('axios.interface') + message.substr(message.length - 3) + i18n.t('axios.errorText');
    // }
    // Message({ message: message, type: 'error', duration: 5 * 1000 })
    // return Promise.reject(error)
    uni.hideLoading()
    /*  对响应错误做点什么 （statusCode !== 200）*/
    return Promise.reject(response)
  })
}

//  初始化请求配置
const initRequest = (vm, type) => {
  uni.$u.http.setConfig((defaultConfig) => {
    /* defaultConfig 为默认全局配置 */
    defaultConfig.baseURL = config.baseURL /* 根域名 */
    defaultConfig.custom = config.custom
    return defaultConfig
  })
  if (type == 2) return
  requestInterceptors()
  responseInterceptors()
}

export {
  initRequest
}