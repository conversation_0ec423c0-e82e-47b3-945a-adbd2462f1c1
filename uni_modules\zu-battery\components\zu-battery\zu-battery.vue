<template>
	<view class="container-battery">
		<view class="battery-box">
      <view class="dot-box">
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
			<view
				class="battery-content"
				:style="{
					width: widthValue
				}"
			>
				<view
					class="battery-value"
					:style="{
						width: batteryValueWidth,
						height: heightValue,
						backgroundColor: batteryColor
					}"
				></view>
        <view 
        	class="battery-percent"
        	:style="{
        		color: color,
            fontSize,
        	}"
        	v-if="showPercent"
        >
        	{{ batteryText }}
        </view>
			</view>
		</view>
	</view>
</template>
<script>
function	getValue(value) {
		const reg = /(px|rpx)$/;
		const tempValue = value.replace(reg, '');
		return parseInt(tempValue);
}
const getUnit = (value) => {
	if (/rpx$/.test(value)) {
		return 'rpx';
	}
	return 'px';
}

export default {
	props: {
		battery: { type: [String, Number], default: '0' },
		showPercent: { type: Boolean, default: true },
		color: { type: String, default: '#1c1c1c' },
		fontSize: { type: String, default: '10px' },
		width: { type: String, default: '22' },
		height: { type: String, default: '12' },
		batteryValues: {
			type: Array,
			default:  () => {
				return [20, 40, 100]
			}
		},
		batteryColors: {
			type: Array,
			default:  () => {
				// return  ['#ff0000','#ffff00', '#4cd964']
				return  ['#229bff','#229bff', '#229bff']
			}
		}
	},
	computed: {
		batteryText() {
				if(this.battery < 0) {
					return '0%'
				}
				if(this.battery > 100) {
					return '100%'
				}
				return this.battery + '%'
		},
		widthValue() {
			return getValue(this.width) + getUnit(this.width)
		},
		heightValue() {
			return (getValue(this.height) - 4) + getUnit(this.height)
		},
		batteryValueWidth() {
			return (getValue(this.width) * this.battery) / 100 + getUnit(this.width);
		},
		batteryColor() {
			for(let i = 0; i <  this.batteryValues.length; i++ ) {
				if(this.battery <= this.batteryValues[i]) {
					return this.batteryColors[i]
				}
			}
			return this.batteryColors[this.batteryColors.length -1]
		},
		dotTop() {
			return (getValue(this.height) - 4) / 2 + getUnit(this.height)
		}
	}
}
</script>
<style scoped lang="scss">
.container-battery {
	display: flex;
	flex-direction: row;
	align-items: center;
}
.battery-percent {
	font-size: 10px;
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  transform: rotate(90deg);
  display: flex;
  align-items: center;
  justify-content: center;
}
.battery-content {
	padding: 1px;
	border-radius: 3px;
	border: 2px solid #229bff;
	display: flex;
	flex-direction: row;
}
.battery-box {
	position: relative;
	// padding-right: 4px;
  transform: rotate(270deg);
  background-color: #fff;
  border-radius: 1px;
}
.dot-box {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  position: absolute;
  right: -5px;
  height: 100%;
  padding: 3px 0;
}
.dot {
	background-color: #229bff;
	width: 4px;
	height: 7px;
	// position: absolute;
	// right: 2px;
	// top: 4px;
	border-radius: 3px;
}
.battery-value {
	height: 8px;
	border-radius: 1px;
	overflow: hidden;
}
</style>
