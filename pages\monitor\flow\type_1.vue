<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex align-items">
        <view style="width: 80rpx;height: 100rpx;" class="flex u-flex-column-reverse" v-if="isShowDiesel">
          <image src="../../../static/flow_diesel.png" style="width: 90rpx;height: 70rpx;"></image>
        </view>
        <image src="../../../static/flow_ac.png" mode="" v-else></image>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line mb-20" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
      <view class="le-load-line" :style="[lineStyle('load')]">
        <template v-if="showCircle('load')">
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="rightToLoad"
            v-if="flowData.load < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToLoad"
            v-else-if="flowData.load > 1"></u-icon>
        </template>
      </view>
      <view class="le-load flex align-items">
        <image src="../../../static/flow_load.png" mode=""></image>
        <view class="ml-5">{{ flowData.load }} kW</view>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-pv flex align-items u-flex-row-reverse">
        <image src="../../../static/flow_pv.png" mode=""></image>
        <view class="u-m-r-10">{{ flowData.photovoltaic }} kW</view>
      </view>
      <view class="ri-pv-line mb-20" :style="[lineStyle('photovoltaic')]">
        <template v-if="showCircle('photovoltaic')">
          <u-icon name="arrow-down-fill" size="20rpx" color="#fb560a" class="rightToPv"
            v-if="flowData.photovoltaic > 1"></u-icon>
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="leftToPv"
            v-else-if="flowData.photovoltaic < -1"></u-icon>
        </template>
      </view>
      <view class="ri-cell-line" :style="[lineStyle('cell')]">
        <template v-if="showCircle('cell')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToCell"
            v-if="flowData.cell < -1"></u-icon>
          <u-icon name="arrow-up-fill" size="20rpx" color="#fb560a" class="leftToCell"
            v-else-if="flowData.cell > 1"></u-icon>
        </template>
      </view>
      <view class="ri-cell flex align-items u-flex-row-reverse">
        <view
          style="height: 85rpx;width: 85rpx;display: flex;justify-content: center;align-items: center;margin-top: 10rpx;">
          <zu-battery :battery="soc" width="54rpx" height="54rpx" fontSize="8px"
            style="margin-bottom: 10rpx;"></zu-battery>
        </view>
        <view class="u-m-r-10">
          <span>{{ flowData.cell }}</span> kW
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    isShow3502Bit10
  } from '@/common/parseBinaryToText.js'
  import { calculateAverageOfAverages } from '@/common/utils.js';

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    return deviceType.value == 10000 ? monitorStore.groupFlowData : monitorStore.flowData
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let borderColor = ''
    switch (type) {
      case 'power':
        borderColor = `transparent transparent ${color} ${color}`
        break;
      case 'load':
        borderColor = `${color} transparent transparent ${color}`
        break;
      case 'photovoltaic':
        borderColor = `transparent ${color} ${color} transparent`
        break;
      case 'cell':
        borderColor = `${color} ${color} transparent transparent`
        break;
    }
    return {
      borderColor
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = deviceType.value == 10000 ? monitorStore.groupControl[0] : monitorStore.control
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 <= flowData.value[name] && flowData.value[name] <= 1) {
        return false
      } else if (flowData.value[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  // 是否显示发电机
  const isShowDiesel = computed(() => {
    let sts = monitorStore.pcs_sts
    if (sts.length) return isShow3502Bit10(sts[0]['sts_3502'])
    return false
  })

  const soc = computed(() => {
    let bms = monitorStore.pcs_bms;
    let bmsBau = monitorStore.pcs_bmsBau;
    if (bms.length) return calculateAverageOfAverages(bms);
    else if (bmsBau.length) return calculateAverageOfAverages(bmsBau);
    return 0;
  });

  if (deviceType.value == 10000) {
    monitorStore.selectDynamicGraphGroupFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type,
      groupId: monitorStore.routeQuery.groupId,
      groupType: monitorStore.routeQuery.groupType
    })
  } else {
    monitorStore.selectDynamicGraphFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type
    })
  }
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 400rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 10rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;

      .le-gird {
        position: relative;
        left: -30rpx;
        top: 14rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 80rpx;
        width: 250rpx;
        border: 4rpx solid;
        border-color: transparent transparent #fb560a #fb560a;
        border-radius: 0 20rpx;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -14rpx;
            left: -12rpx;
            transform: rotate(0);
          }

          47% {
            top: calc(100% - 40rpx);
            left: -12rpx;
            transform: rotate(0);
          }

          50% {
            top: calc(100% - 10rpx);
            left: -12rpx;
            transform: rotate(-45deg);
          }

          53% {
            top: calc(100% - 8rpx);
            left: 8rpx;
            transform: rotate(-90deg);
          }

          100% {
            top: calc(100% - 8rpx);
            left: calc(100% - 12rpx);
            transform: rotate(-90deg);
          }
        }

        @keyframes leftToGird {
          100% {
            top: -14rpx;
            left: -13rpx;
            transform: rotate(90deg);
          }

          53% {
            top: calc(100% - 48rpx);
            left: -13rpx;
            transform: rotate(90deg);
          }

          50% {
            top: calc(100% - 16rpx);
            left: -10rpx;
            transform: rotate(45deg);
          }

          47% {
            top: calc(100% - 7rpx);
            left: -10rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 7rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }

      .le-load {
        position: relative;
        left: -44rpx;
        bottom: 18rpx;

        image {
          width: 90rpx;
          height: 80rpx;
        }
      }

      .le-load-line {
        position: relative;
        height: 90rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a transparent transparent #fb560a;
        border-radius: 20rpx 0;

        .rightToLoad {
          position: absolute;
          animation: rightToLoad 3s linear infinite;
        }

        .leftToLoad {
          position: absolute;
          animation: leftToLoad 3s linear infinite;
        }

        @keyframes rightToLoad {
          100% {
            top: -12rpx;
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }

          53% {
            top: -12rpx;
            left: 0;
            transform: rotate(90deg);
          }

          50% {
            top: 0rpx;
            left: -10rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: 14rpx;
            left: -12rpx;
            transform: rotate(0);
          }

          0% {
            top: calc(100% - 14rpx);
            left: -12rpx;
            transform: rotate(0);
          }
        }

        @keyframes leftToLoad {
          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(-90deg);
          }

          53% {
            top: 8rpx;
            left: -10rpx;
            transform: rotate(-90deg);
          }

          50% {
            top: 0;
            left: -8rpx;
            transform: rotate(-45deg);
          }

          47% {
            top: -11rpx;
            left: 8rpx;
            transform: rotate(0);
          }

          0% {
            top: -11rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;

      .ri-pv {
        margin-top: 16rpx;
        z-index: 0;
        position: relative;
        right: -32rpx;
        top: 10rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 90rpx;
        }
      }

      .ri-pv-line {
        position: relative;
        height: 80rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: transparent #fb560a #fb560a transparent;
        border-radius: 10px 0;

        .rightToPv {
          position: absolute;
          animation: rightToPv 3s linear infinite;
        }

        .leftToPv {
          position: absolute;
          animation: leftToPv 3s linear infinite;
        }

        @keyframes rightToPv {
          0% {
            top: -12rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }

          47% {
            top: calc(100% - 30rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }

          50% {
            top: calc(100% - 16rpx);
            left: calc(100% - 14rpx);
            transform: rotate(45deg);
          }

          53% {
            top: calc(100% - 8rpx);
            left: calc(100% - 18rpx);
            transform: rotate(90deg);
          }

          100% {
            top: calc(100% - 8rpx);
            left: -10rpx;
            transform: rotate(90deg);
          }
        }

        @keyframes leftToPv {
          0% {
            top: calc(100% - 6rpx);
            left: -7px;
            transform: rotate(0);
          }

          47% {
            top: calc(100% - 6rpx);
            left: calc(100% - 20rpx);
            transform: rotate(0);
          }

          50% {
            top: calc(100% - 14rpx);
            left: calc(100% - 14rpx);
            transform: rotate(-45deg);
          }

          53% {
            top: calc(100% - 20rpx);
            left: calc(100% - 6rpx);
            transform: rotate(-90deg);
          }

          100% {
            top: -8rpx;
            left: calc(100% - 6rpx);
            transform: rotate(-90deg);
          }
        }
      }

      .ri-cell {
        position: relative;
        right: -41rpx;
        bottom: 24rpx;

        &-color {
          color: $u-primary;
        }

        image {
          width: 85rpx;
          height: 85rpx;
        }
      }

      .ri-cell-line {
        position: relative;
        height: 80rpx;
        width: 250rpx;
        border: 2px solid;
        border-color: #fb560a #fb560a transparent transparent;
        border-radius: 0 10px;

        .rightToCell {
          position: absolute;
          animation: rightToCell 3s linear infinite;
        }

        .leftToCell {
          position: absolute;
          animation: leftToCell 3s linear infinite;
        }

        @keyframes rightToCell {
          0% {
            top: -10rpx;
            left: -8rpx;
            transform: rotate(0);
          }

          47% {
            top: -10rpx;
            left: calc(100% - 28rpx);
            transform: rotate(0);
          }

          50% {
            top: 2rpx;
            left: calc(100% - 12rpx);
            transform: rotate(45deg);
          }

          53% {
            top: 10rpx;
            left: calc(100% - 8rpx);
            transform: rotate(90deg);
          }

          100% {
            top: calc(100% - 4rpx);
            left: calc(100% - 10rpx);
            transform: rotate(90deg);
          }
        }

        @keyframes leftToCell {
          0% {
            top: calc(100% - 8rpx);
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }

          47% {
            top: 8rpx;
            left: calc(100% - 8rpx);
            transform: rotate(0);
          }

          50% {
            top: -8rpx;
            left: calc(100% - 14rpx);
            transform: rotate(-45deg);
          }

          53% {
            top: -12rpx;
            left: calc(100% - 20rpx);
            transform: rotate(-90deg);
          }

          100% {
            top: -12rpx;
            left: -8rpx;
            transform: rotate(-90deg);
          }
        }
      }
    }
  }
</style>