<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('方案名称')" prop="name" :borderBottom="true">
          <u-input v-model="form.name" :placeholder="$t('请输入方案名称')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('电网充电')" prop="setting1901" :borderBottom="true">
          <u-radio-group v-model="form.setting1901" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('使能')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('不使能')" name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item :label="$t('发电机')" prop="setting1902" :borderBottom="true">
          <u-radio-group v-model="form.setting1902" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('使能')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('不使能')" name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item :label="$t('发电机充电')" prop="setting1903" :borderBottom="true">
          <u-radio-group v-model="form.setting1903" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('使能')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('不使能')" name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item :label="$t('电池充电功率')" prop="setting1904" :borderBottom="true" class="rate">
          <u-number-box v-model="form.setting1904" :step="1" :min="0" :max="500" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item :label="$t('备电保持SOC')" prop="setting1905" class="rate">
          <u-number-box v-model="form.setting1905" :min="0" :max="100" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    addBackup,
    lookBackup,
    editBackup
  } from '@/api/backup'
  import { inputNumberStyle } from '@/constant'

  const title = ref(uni.$t('添加备电方案'))
  const id = ref()
  onLoad((options) => {
    if (options.id) {
      id.value = options.id
      title.value = uni.$t('修改备电方案')
    } else {
      title.value = uni.$t('添加备电方案')
    }
  })
  onShow(() => {
    if (title.value == uni.$t('修改备电方案')) getInfoFn()
  })

  const getInfoFn = async () => {
    const res = await lookBackup({
      id: id.value
    })
    form.value = res.data
  }

  const formRef = ref(null)
  const form = ref({
    name: '',
    setting1901: '0',
    setting1902: '0',
    setting1903: '0',
    setting1904: undefined,
    setting1905: undefined,
  })
  const rules = ref({
    'name': {
      type: 'string',
      required: true,
      message: uni.$t('请输入方案名称'),
      trigger: ['blur', 'change'],
    },
    'setting1901': {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'setting1902': {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'setting1903': {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'setting1904': {
      type: 'number',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'setting1905': {
      type: 'number',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加备电方案')) {
            addBackup({
              name: form.value.name,
              setting1901: form.value.setting1901,
              setting1902: form.value.setting1902,
              setting1903: form.value.setting1903,
              setting1904: form.value.setting1904,
              setting1905: form.value.setting1905,
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editBackup({
              name: form.value.name,
              id: form.value.id,
              setting1901: form.value.setting1901,
              setting1902: form.value.setting1902,
              setting1903: form.value.setting1903,
              setting1904: form.value.setting1904,
              setting1905: form.value.setting1905,
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }
</style>