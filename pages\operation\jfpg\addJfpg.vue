<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('方案名称')" prop="title" ref="item1">
          <u-input v-model="form.title" :placeholder="$t('请输入方案名称')" border="none"></u-input>
        </u-form-item>
      </view>
      <view class="flex u-flex-between u-flex-items-center u-m-t-20 u-m-b-20">
        <view style="font-size: 14px;font-weight: bold;">{{ $t('时段信息') }}</view>
        <view class="flex">
          <u-button type="primary" :plain="true" :text="$t('添加')" shape="circle" class="u-m-r-20 btn"
            @click="handleAddTimeClick"></u-button>
          <u-button type="error" :plain="true" :text="$t('删除')" shape="circle" class="btn"
            @click="handleDeleteTimeClick"></u-button>
        </view>
      </view>
      <view class="add-item mb-20" v-for="(item, index) in form.pointList" :key="index + 'time'">
        <u-form-item :label="$t('开始时间')" :prop="`pointList.${index}.startTime`" :borderBottom="true" :rules="[{  
        type: 'string',  
        required: true,  
        message: $t('请选择开始时间'),  
        trigger: ['blur', 'change'],  
      }]">
          <u-datetime-picker hasInput :show="showTime" v-model="item.startTime" mode="time"
            itemHeight="88"></u-datetime-picker>
        </u-form-item>
        <u-form-item :label="$t('结束时间')" :prop="`pointList.${index}.endTime`" :borderBottom="true" :rules="[{
          		  type: 'string',  
          		  required: true,  
          		  message: $t('请选择结束时间'),  
          		  trigger: ['blur', 'change'],  
          		}]">
          <u-datetime-picker hasInput :show="showTime" v-model="item.endTime" mode="time"
            itemHeight="88"></u-datetime-picker>
        </u-form-item>
        <u-form-item :label="$t('功率')" :prop="`pointList.${index}.power`" :borderBottom="true" :rules="[{
          		  type: 'string',  
          		  required: true,  
          		  message: $t('请输入功率'),  
          		  trigger: ['blur', 'change'],  
          		}]">
          <u-input v-model="item.power" :placeholder="$t('请输入功率')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('是否使能')" :prop="`pointList.${index}.enable`" borderBottom :rules="[{
                type: 'number',
          		  required: true,  
          		  message: $t('请选择是否使能'),  
          		  trigger: ['blur', 'change'],  
          		}]">
          <u-radio-group v-model="item.enable" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('使能')" :name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('不使能')" :name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%;text-align: center;">{{ $t('注：功率：分正负，正为放电，负为充电') }}</view>
  </view>
</template>

<script setup>
  import {
    ref
  } from 'vue'
  import {
    onLoad, onShow
  } from '@dcloudio/uni-app'
  import {
    addJfpg,
    lookJfpg,
    editJfpg
  } from '@/api/jfpg'
  
  const title = ref(uni.$t('添加削峰填谷'))
  const id = ref()
  onLoad((options) => {
    if (options.id) {
      id.value = options.id
      title.value = uni.$t('修改削峰填谷')
    } else {
      title.value = uni.$t('添加削峰填谷')
    }
  })
  onShow(() => {
    if (title.value == uni.$t('修改削峰填谷')) getInfoFn()
  })
  
  const getInfoFn = async () => {
    const res = await lookJfpg({ id: id.value })
    form.value = res.data.length ? res.data[0]: {}
  }

  const formRef = ref(null)
  const form = ref({
    title: '',
    pointList: [{
      enable: 1,
      endTime: '',
      power: '',
      startTime: ''
    }]
  })
  const rules = ref({
    'title': {
      type: 'string',
      required: true,
      message: uni.$t('请输入方案名称'),
      trigger: ['blur', 'change'],
    },
  })

  const showTime = ref(false)
  const handleAddTimeClick = () => {
    if (form.value.pointList.length == 12) return uni.$u.toast(uni.$t('最多只能添加12条哦'))
    form.value.pointList.push({
      enable: 1,
      endTime: '',
      power: '',
      startTime: ''
    })
  }
  const handleDeleteTimeClick = () => {
    if (form.value.pointList.length == 1) return uni.$u.toast(uni.$t('至少要有一条哦'))
    form.value.pointList.pop()
  }

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加削峰填谷')) {
            addJfpg({
              title: form.value.title,
              pointList: form.value.pointList,
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editJfpg({
              title: form.value.title,
              pointList: form.value.pointList,
              id: form.value.id
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }
</style>