{"id": "uv-calendar", "displayName": "uv-calendar 日历 全面兼容vue3+2、app、h5、小程序等多端", "version": "1.0.6", "description": "日历组件用于单个选择日期，范围选择日期等，日历被包裹在底部弹起的容器中，灵活配置，功能齐全，兼容全端。强烈推荐使用最新版日历组件，在下方跳入。", "keywords": ["uv-calendar", "uvui", "uv-ui", "calendar", "日历"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uv-ui-tools", "uv-button", "uv-popup"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}