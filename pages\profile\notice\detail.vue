<template>
  <view class="notice-detail">

    <view class="detail-container" v-if="noticeInfo">
      <!-- 公告头部信息 -->
      <view class="detail-title">{{ noticeInfo[getPropFn('title')] }}</view>
      <view class="content-text" v-html="noticeInfo[getPropFn('cont')]"></view>
      <view class="detail-meta flex justify-content align-items">
        <view class="meta-left">
          <view class="detail-time">{{ $t('发布时间') }}：{{ noticeInfo.createTime }}</view>
          <view class="detail-author">{{ $t('发布人') }}：{{ $t('系统管理员') }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    onMounted,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad
  } from '@dcloudio/uni-app'
  import {
    getNotice
  } from '@/api/notice.js'
  import otherColor from '../../../common/other.module.scss'

  const {
    proxy
  } = getCurrentInstance()

  const noticeInfo = ref(null)
  const noticeId = ref('')

  /**
   * 获取公告详情
   */
  const getNoticeDetail = () => {
    if (!noticeId.value) return

    // 使用模拟数据进行测试，实际项目中使用 getNoticeInfo
    getNotice(noticeId.value).then(res => {
      noticeInfo.value = res.data || res
    })
  }

  const getPropFn = (type) => {
    const lang = uni.cache.getItem('language')
    if (type == 'title') {
      switch (lang) {
        case 'zh':
          return 'noticeTitle'
        case 'en':
          return 'noticeTitleUs'
        case 'it':
          return 'noticeTitleIt'
        default:
          return 'noticeTitle'
      }
    } else {
      switch (lang) {
        case 'zh':
          return 'noticeContent'
        case 'en':
          return 'noticeContentUs'
        case 'it':
          return 'noticeContentIt'
        default:
          return 'noticeContent'
      }
    }
  }

  onLoad((options) => {
    noticeId.value = options.noticeId
    getNoticeDetail()
  })
</script>

<style scoped lang="scss">
  .notice-detail {
    height: 100vh;
    overflow: auto;

    .navbar-right {
      padding: 0 15rpx;
    }

    .detail-container {
      padding: 20rpx 30rpx;
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 30rpx;
      margin: 20rpx;
    }

    .detail-title {
      font-size: 18px;
      font-weight: bold;
      color: $uni-text-color;
      line-height: 1.4;
      margin-bottom: 30rpx;
      text-align: center;
      padding-bottom: 20rpx;
      border-bottom: 1px solid #ebebeb;
    }

    .detail-meta {
      .meta-left {
        flex: 1;

        .detail-time,
        .detail-author {
          font-size: 12px;
          color: $uni-text-color-grey;
          margin-bottom: 8rpx;
        }
      }
    }

    .content-text {
      font-size: 14px;
      color: $uni-text-color;
      line-height: 1.6;
      word-break: break-all;

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: $uni-border-radius-base;
      }

      :deep(p) {
        margin-bottom: 20rpx;
      }
    }
  }
</style>