<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-bus flex u-flex-column align-items">
        <image src="../../../static/flow_dc.png" mode=""></image>
        <view class="ml-5">{{ flowData.bus }} kW</view>
      </view>
      <view class="le-bus-line" :style="[lineStyle('bus')]">
        <template v-if="showCircle('bus')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToBus"
            v-if="flowData.bus < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToBus"
            v-else-if="flowData.bus > 1"></u-icon>
        </template>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-pv flex u-flex-column align-items">
        <image src="../../../static/flow_pv.png" mode=""></image>
        <view class="u-m-r-10">{{ flowData.photovoltaic }} kW</view>
      </view>
      <view class="ri-pv-line" :style="[lineStyle('photovoltaic')]">
        <template v-if="showCircle('photovoltaic')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToPv"
            v-if="flowData.photovoltaic > 1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToPv"
            v-else-if="flowData.photovoltaic < -1"></u-icon>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import { calculateAverageOfAverages } from '@/common/utils.js';

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    return deviceType.value == 10001 ? monitorStore.groupFlowData : monitorStore.flowData
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let backgroundColor = color
    return {
      backgroundColor
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = deviceType.value == 10001 ? monitorStore.groupControl[0] : monitorStore.control
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 < flowData.value[name] && flowData.value[name] < 1) {
        return false
      } else if (flowData.value[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  const soc = computed(() => {
    let bms = monitorStore.pcs_bms;
    let bmsBau = monitorStore.pcs_bmsBau;
    if (bms.length) return calculateAverageOfAverages(bms);
    else if (bmsBau.length) return calculateAverageOfAverages(bmsBau);
    return 0;
  });

  if (deviceType.value == 10001) {
    monitorStore.selectDynamicGraphGroupFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type,
      groupId: monitorStore.routeQuery.groupId,
      groupType: monitorStore.routeQuery.groupType
    })
  } else {
    monitorStore.selectDynamicGraphFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type
    })
  }
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 250rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 24rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;
      bottom: 50rpx;

      .le-bus {
        position: relative;
        top: 68rpx;
        left: -116rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-bus-line {
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;
        position: relative;

        .rightToBus {
          position: absolute;
          animation: rightToBus 3s linear infinite;
        }

        .leftToBus {
          position: absolute;
          animation: leftToBus 3s linear infinite;
        }

        @keyframes rightToBus {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }

        @keyframes leftToBus {
          100% {
            top: -7rpx;
            left: -12rpx;
          }

          0% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      bottom: 50rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .ri-pv {
        margin-top: 20rpx;
        z-index: 0;
        position: relative;
        right: -60rpx;
        top: 64rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 80rpx;
        }
      }

      .ri-pv-line {
        position: relative;
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;

        .rightToPv {
          position: absolute;
          animation: rightToPv 3s linear infinite;
        }

        .leftToPv {
          position: absolute;
          animation: leftToPv 3s linear infinite;
        }

        @keyframes rightToPv {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
        }

        @keyframes leftToPv {
          0% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }

          100% {
            top: -7rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
</style>