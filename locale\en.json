{"app.name": "Elecloud", "tabbar.tabbar1": "Overview", "tabbar.tabbar3": "Property", "tabbar.tabbar2": "Monitoring", "tabbar.tabbar4": "Setting", "home.title": "Project Overview", "累计放电量": "Cumulative Discharge Capacity", "累计充电量": "Cumulative Charge Capacity", "光伏发电量": "PV Power Generation", "总收益": "Total Revenue", "总装机容量": "Total Installed Capacity", "总装机功率": "Total Installed Power", "设备总数": "Total Number Of Devices", "项目汇总": "Project Summary", "设备": "<PERSON><PERSON>", "国家": "Country", "元": "CNY", "搜索": "Search", "确定": "Confirm", "取消": "Cancel", "系统提示": "System Prompt", "正常": "Normal", "故障": "<PERSON><PERSON>", "修改": "Edit", "删除": "Delete", "新建": "Add", "重置": "Reset", "操作": "Operate", "状态": "State", "单位": "Unit", "详细地址": "Full address", "请输入": "Please enter", "离线": "Offline", "在线": "Online", "全部": "All", "保存": "Save", "添加成功": "Added successfully!", "添加失败": "Addition Failed!", "修改成功": "Modify successfully!", "修改失败": "Change failed!", "删除成功": "Deleted successfully", "删除失败": "Deleted Failed", "已取消删除": "Deletion Cancelled", "请选择": "Select", "查看": "Check", "开启": "Running", "关闭": "Closure", "并网": "Grid connection", "离网": "Off-grid", "停机": "Shutdown", "运行": "Running", "确认添加": "Confirm addition", "提交": "Submit", "返回": "Back", "选择": "Select", "电池可启动": "Battery Startable", "电池不可启动": "Battery does not start", "放电": "Discharge", "充电": "Charge", "启用": "Enable", "停用": "Deactivate", "闭合": "Closure", "断开": "Switch Off", "导出": "Export", "导出报表": "Export Report", "时区": "Time Zone", "时区地址": "Time Zone Address", "停止": "Stop", "周": "Week", "月": "Month", "年": "Year", "日": "Day", "设备监控": "Device Monitoring", "在线数": "Online", "离线数": "Offline", "设备监控列表": "Device Monitoring List", "电池装机容量": "Installed Battery  Capacity", "额定功率": "Rated Power", "系统运行状态": "System Running Status", "储能充电量": "Energy Storage Charge Capacity", "储能放电量": "Energy Storage Discharge Capacity", "光伏装机容量": "PV Installed Capacity", "电网功率": "Grid Power", "光伏功率": "PV power", "负载功率": "Load Power", "电池功率": "Battery Power", "直流母线功率": "DC Bus Power", "功率分析": "Power Analysis", "电量统计": "Power Statistics", "充电量": "Charge Capacity", "放电量": "Discharge Capacity", "光伏": "PV", "储能": "Energy Storage", "光储": "PV Storage", "电压": "U", "温度": "TEMP", "电芯": "Battery Cell", "额定容量": "Rated Capacity", "在线状态": "Online status", "工作状态": "Working status", "电池可充电": "Battery rechargeable", "电池不可充电": "Battery is not chargeable", "电池可放电": "Battery dischargeable", "电池不可放电": "The battery cannot be discharged", "本地控制器": "Local Controller", "设备信息": "Device Info", "系统工作模式": "System Working Mode", "系统故障状态": "System Fault Status", "系统告警状态": "System Alarm Status", "并离网状态": "On-Grid And Off-Grid Status", "AC运行状态": "AC Running Status", "DC运行状态": "DC Running Status", "系统状态": "System Status", "电池状态": "Battery Status", "电池高压状态": "Battery High Voltage status", "通讯状态": "Communication Status", "空调状态": "Air Conditioning Status", "水浸状态": "Flooded State", "烟感状态": "Smoke State", "防雷": "Lightning Protection", "消防动作": "Fire Fighting Action", "储能功率": "Energy Storage Power", "软件版本": "Software version", "基本信息": "Base Information", "电网总无功功率": "Total reactive power of the grid", "电网总有功功率": "Total active power of the grid", "电网频率A": "Grid Frequency A", "电网频率B": "Grid Frequency B", "电网频率C": "Grid Frequency C", "未上高压": "Not on the high pressure", "上高压": "On the high pressure", "风机": "Blowers", "制冷": "Cooling", "加热": "Heat up", "除湿": "Dehumidify", "策略状态": "Policy State", "未使用": "Unused", "未运行": "Not Run", "工作模式": "Operating Mode", "充放电": "Discharge", "运行状态": "Running Status", "故障状态": "Fault Status", "直流电压": "DC Voltage", "直流电流": "DC Current", "直流功率": "DC Power", "线电压Vab": "Line Voltage Vab", "线电压Vbc": "Line Voltage Vbc", "线电压Vca": "Line Voltage Vca", "A相电流": "A Phase Current", "B相电流": "B Phase Current", "C相电流": "C Phase Current", "有功功率": "Active power", "无功功率": "Reactive power", "视在功率": "Inspecting Power", "频率": "Frequency", "igbt温度": "igbt Temperature", "功率因数": "Power Factor", "功率因数A": "Power Factor A", "功率因数B": "Power Factor B", "功率因数C": "Power Factor C", "正极对地阻抗值": "Positive electrode to ground impedance value", "负极对地阻抗值": "Negative pole to ground impedance value", "版本号": "Version Number", "接入设备": "Access Device", "DC低压侧1路电压": "DC low voltage side 1 voltage", "DC低压侧1路电流": "DC low voltage side 1 current", "DC低压侧1路功率": "DC low voltage side 1 channel power", "DC低压侧2路电压": "DC low voltage side 2 voltage", "DC低压侧2路电流": "DC low voltage side 2 current", "DC低压侧2路功率": "DC low voltage side 2 channel power", "DC高压侧电压": "DC high side voltage", "DC高压侧电流": "DC high voltage side current", "DC高压侧功率": "DC high voltage side power", "高压侧类型": "High voltage side type", "低压侧类型": "Low voltage side type", "锂电池": "Lithium-ion battery", "直流母线": "DC Busbar", "直流源": "DC Source", "铅酸电池": "Lead Battery", "通信状态": "Communication Status", "设备状态": "Device Status", "电网电压A(AB)": "Grid Voltage A(AB)", "电网电压B(BC)": "Grid Voltage B(BC)", "电网电压C(CA)": "Grid Voltage C(CA)", "电网电流A": "Grid Current A", "电网电流B": "Grid Current B", "电网电流C": "Grid Current C", "电网有功A": "Grid Active Power A", "电网有功B": "Grid Active Power B", "电网有功C": "Grid Active Power C", "电网无功A": "Grid Reactive Power A", "电网无功B": "Grid Reactive Power B", "电网无功C": "Grid Reactive Power C", "电网总有功": "The power grid always works", "电网总无功": "Total reactive power of the grid", "电网总视在功率": "Total apparent power of the power grid", "电网视在功率A": "Grid Apparent Power A", "电网视在功率B": "Grid Apparent Power B", "电网视在功率C": "Grid Apparent Power C", "负载电压A(AB)": "Load Voltage A(AB)", "负载电压B(BC)": "Load Voltage B(BC)", "负载电压C(CA)": "Load Voltage C(CA)", "总功率因素": "Total Power Factor", "电网频率": "<PERSON><PERSON>", "BMS通信状态": "BMS Communication Status", "电池电压": "Battery Voltage", "电池电流": "Battery Current", "电池可充电量": "Battery Rechargeable Capacity", "充电限制电流": "Charge Limit Current", "放电限制电流": "Discharge Limit <PERSON>", "电池可放电量": "Battery Discharge Capacity", "最小单体电压": "Minimum Cell Voltage", "最大单体电压": "Maximum Cell Voltage", "平均单体电压": "Average Cell Voltage", "最高单体温度": "Maximum Cell Temperature", "最低单体温度": "Minimum Cell Temperature", "平均单体温度": "Average Cell Temperature", "最低单体电压位置": "Minimum Cell Voltage Position", "最高单体电压位置": "Highest Cell Voltage Position", "电芯压差": "Cell Voltage Difference", "最高单体温度位置": "Maximum Cell Temperature Position", "最低单体温度位置": "Minimum Cell Temperature Position", "电芯温差": "Cell Temperature Difference", "环境温度(电池仓)": "Ambient Temperature (Battery Compartment)", "环境湿度(电池仓)": "Ambient Humidity (Battery Compartment)", "绝缘阻抗值": "Insulation Impedance Value", "电表": "Ammeter", "项目名称": "Project Name", "项目地址": "Project Address", "区域": "Area", "经纬度": "Latitude And Longitude", "设备SN码": "SN", "请输入项目名称": "Please enter project name", "添加项目": "Add Item", "修改项目": "Modify Project", "请输入详细地址": "Please enter full address", "请输入项目地址": "Please enter project address", "请输入经度": "Please enter longitude", "请输入纬度": "Please enter latitude", "设备名称": "Device Name", "整机序列号": "Machine Serial Number", "设备型号": "Device Model", "设备类型": "Device Type", "电池容量": "Battery Capacity", "出厂软件版本": "Factory Software Version", "出厂硬件版本": "Factory Hardware Version", "客户单号": "Customer Order Number", "订单号": "Order Number", "订单特殊说明": "Special Instructions For Orders", "模块序列号": "Module Serial Number", "实时功率": "Real-time Power", "协议版本": "Protocol Version", "设备地址": "<PERSON><PERSON> Address", "序列号": "SN", "选择设备": "Select Device", "此操作将永久删除该项目，该项目的设备记录将保留，如要删除设备，请到设备入库删除！ 是否继续?": "This operation will permanently delete the project, and the equipment records of the\nproject will be retained.If you want to delete the equipment, please go to the equipment storage to delete it! Whether to continue?", "此操作将永久注销该设备, 是否继续?": "This operation will permanently log out the device. Do you want to continue?", "请输入设备序列号": "Please enter device serial number", "请输入整机序列号": "Please enter machine serial number (screen)", "请输入设备名称": "Please enter device name", "请输入设备型号": "Please enter device model", "请输入光伏装机容量": "Please enter PV installed capacity", "请输入电池容量": "Please enter battery capacity", "请输入额定功率": "Please enter rated power", "请输入出厂软件版本": "Please enter factory software version", "请输入出厂硬件版本": "Please enter factory hardware version", "请输入订单号": "Please enter order number", "请输入订单说明": "Please enter special instructions for orders", "请输入模块序列号": "Please enter module serial number", "请输入客户单号": "Please enter customer order number", "添加设备": "Add <PERSON>", "修改设备": "Modify Device", "查询设备": "Querying Device", "超过10个": "More Than 10", "未处理": "Pending", "已处理": "Processed", "告警等级": "Alarm Level", "设备序列号": "SN", "告警状态": "Alarm Status", "发生时间": "Start Time", "上报时间": "Reporting Time", "告警名称": "Alarm Name", "告警对象": "Alarm Object", "所属项目": "Affiliated Projects", "请输入告警名称": "Please enter alarm name", "等级一": "Level One", "等级二": "Level Two", "处理": "Deal With", "处理成功": "Processed successfully!", "确定要手动处理这条异常吗？": "Are you sure you want to handle this exception manually?", "告警": "Alarm", "个人中心": "Personal Center", "退出登录": "Sign Out", "此操作将永久注销该设备，是否继续？": "This operation will permanently log out the device. Do you want to continue?", "设备详情": "<PERSON>ce Det<PERSON>", "请求数据大小超出允许的5M限制，无法进行防重复提交验证。": "The requested data size exceeds the allowed 5M limit, and the anti-duplicate submission\n verification cannot be performed.", "数据正在处理，请勿重复提交": "Data is being processed, please do not resubmit.", "登录状态已过期，您可以继续留在该页面，或者重新登录": "The login status has expired. You can continue to stay on this page or log in again.", "重新登录": "Re-login", "无效的会话，或者会话已过期，请重新登录。": "Invalid session, or the session has expired, please log in again.", "后端接口连接异常": "Backend interface connection abnormality.", "系统接口请求超时": "System interface request timeout.", "系统接口": "System interface", "异常": "abnormal", "正在下载数据，请稍候": "Downloading data, please wait.", "下载文件出现错误，请联系管理员！": "There was an error downloading the file, please contact the administrator!", "用户名称": "User Name", "用户昵称": "User's Nickname", "手机号码": "Phone Number", "用户编号": "User ID", "部门": "Department", "重置密码": "Reset Password", "分配角色": "Assigning Roles", "岗位": "Post", "归属部门": "Belonging Department", "请输入用户名称": "Please enter user name", "请输入用户昵称": "Please enter user nickname", "请输入手机号码": "Please enter phone number", "请输入邮箱": "Please enter email", "请输入用户密码": "Please enter user password", "请输入内容": "Please enter content", "请选择岗位": "Please select post", "请选择归属部门": "Please select belonging department", "请选择角色": "Please select role", "用户状态": "User State", "用户密码": "User Password", "角色": "Role", "备注": "Remark", "邮箱": "Email", "将文件拖到此处，": "Drag files here,", "或": "or", "点击上传": "Click to Upload", "是否更新已经存在的用户数据": "Whether to update existing user data", "仅允许导入xls、xlsx格式文件。": "Only xls and xlsx format files are allowed to be imported.", "下载模板": "Download Template", "用户名称不能为空": "Username cannot be empty", "用户昵称不能为空": "Nickname cannot be empty", "用户密码不能为空": "User password cannot be empty", "用户名称长度必须介于 2 和 20 之间": "Username length must be between 2 and 20", "用户密码长度必须介于 5 和 20 之间": "User password length must be between 5 and 20", "请输入正确的邮箱地址": "Please enter a valid email address", "请输入正确的手机号码": "Please enter a valid mobile phone number", "请输入新密码": "Please enter your new password", "用户导入": "User Import", "导入结果": "Import results", "个人信息": "Personal Information", "用户邮箱": "User Email", "所属角色": "Role", "创建日期": "Create Date", "changePassword": "Change Password", "旧密码": "Old Password", "新密码": "New Password", "确认密码": "Confirm Password", "两次输入的密码不一致": "The passwords entered twice are inconsistent", "请输入旧密码": "Please enter your old password", "请确认新密码": "Please confirm the new password", "旧密码不能为空": "Old password cannot be empty", "新密码不能为空": "New password cannot be empty", "确认密码不能为空": "Confirm password cannot be empty", "长度在 6 到 20 个字符": "6 to 20 characters in length", "点击上传头像": "Click to upload avatar", "修改头像": "Edit Avatar", "文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。": "The file format is wrong, please upload the image type, such as: JPG, PNG suffix file.", "数据分析": "Data Analysis", "角色状态": "Role State", "角色编号": "Role Number", "角色名称": "Role Name", "权限字符": "Permission Characters", "显示顺序": "Display Order  ", "数据权限": "Data Permission", "分配用户": "Assign Users", "展开/折叠": "Expand/Collapse", "权限范围": "Scope Of Authority", "请输入角色名称": "Please enter role name", "请输入权限字符": "Please enter permission characters", "角色顺序": "Role Order", "菜单权限": "Menu Permissions", "全选/全不选": "Select all/Unselect all", "父子联动": "Father and son linkage", "加载中，请稍后": "Loading, please wait", "全部数据权限": "All data permissions", "自定数据权限": "Custom data permissions", "本部门数据权限": "Data permissions of this department", "本部门及以下数据权限": "Data permissions for this department and the following", "仅本人数据权限": "Only personal data permissions", "角色名称不能为空": "Role name cannot be empty", "权限字符不能为空": "Permission Characters cannot be empty", "角色顺序不能为空": "Role Order cannot be empty", "添加角色": "Add role", "修改角色": "Edit role", "分配数据权限": "Assign data permissions", "控制器中定义的权限字符，如：": "The authority characters defined in the controller, for example:", "储能侧电压A(AB)": "Energy Storage Side Voltage A(AB)", "储能侧电压 B(BC)": "Energy Storage Side Voltage B(BC)", "储能侧电压 C(CA)": "Energy Storage Side Voltage C(CA)", "储能侧 A 相电流": "Energy Storage Side A Phase Current", "储能侧 B 相电流": "Energy Storage Side B Phase Current", "储能侧 C 相电流": "Energy Storage Side C Phase Current", "储能侧A相有功功率": "Active power of phase A on energy storage side", "储能侧B相有功功率": "Active power of phase B on energy storage side", "储能侧C相有功功率": "Active power of phase C on energy storage side", "储能侧A相无功功率": "Reactive power of phase A on energy storage side", "储能侧B相无功功率": "Reactive power of phase B on energy storage side", "储能侧C相无功功率": "Reactive power of phase C on energy storage side", "储能侧频率 A": "Energy storage side frequency A", "储能侧频率 B": "Energy storage side frequency B", "储能侧频率 C": "Energy storage side frequency C", "储能侧总功率因数": "Energy storage side total power factor", "交流A相电流": "AC phase A current", "交流B相电流": "AC phase B current", "交流C相电流": "AC phase C current", "总功率因数": "Total power factor", "光伏电压": "PV voltage", "光伏电流": "PV current", "仓内环境温度": "Warehouse ambient temperature", "仓内环境湿度": "Ambient humidity in the warehouse", "电池单体最高温度": "Maximum battery cell temperature", "电池单体最低温度": "Minimum battery cell temperature", "电池单体最大电压": "Maximum voltage of battery cell", "电池单体最低电压": "Minimum voltage of battery cell", "电池电量SOC": "Battery level SOC", "参数": "Parameter", "菜单状态": "Menu State", "菜单名称": "<PERSON>u Name", "英文菜单": "English Name", "图标": "Icon", "排序": "Sort", "权限标识": "Permission ID", "组件路径": "Component Path", "上级菜单": "Previous Menu", "菜单类型": "Menu Type", "目录": "Table of contents", "菜单": "<PERSON><PERSON>", "按钮": "<PERSON><PERSON>", "菜单图标": "Menu Icon", "显示排序": "Display Order", "是否外链": "Whether to external link", "是": "YES", "否": "NO", "路由地址": "Routing address", "路由参数": "Routing parameters", "是否缓存": "Whether to cache", "显示状态": "Display State", "请输入菜单名称": "Please enter menu name", "请输入路由地址": "Please enter routing address", "请输入组件路径": "Please enter component Path", "请输入权限标识": "Please enter permission iD", "请输入路由参数": "Please enter routing parameters", "选择上级菜单": "Select previous menu", "点击选择图标": "Click Select icon", "主类目": "Main category", "添加菜单": "<PERSON><PERSON>", "修改菜单": "<PERSON>", "缓存": "<PERSON><PERSON>", "不缓存": "Uncached", "菜单名称不能为空": "Menu name cannot be empty", "菜单顺序不能为空": "Menu sort cannot be empty", "路由地址不能为空": "Route address cannot be empty", "是否确认删除该数据项？": "Are you sure to delete the data item?", "选择是外链则路由地址需要以`http(s)://`开头": "If you select external link, the routing address needs to start with `https)://`", "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头": "The component path to be accessed, such as: `system/user/index`, which is in the `views` directory by default", "访问的组件路径，如：`system/user/index`，默认在`views`目录下": "The component path to be accessed, such as: `system/user/index`, which is in the `views` directory by default", "访问路由的默认传递参数，如：": "<PERSON><PERSON><PERSON> passed parameters for access routes, such as:", "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致": "If `Yes` is selected, it will be cached by `keep-alive` and needs to match the component's `name` and address.", "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问": "Select Hide and the route will not appear in the sidebar but will still be accessible.", "选择停用则路由将不会出现在侧边栏，也不能被访问": "If 'Disable' is selected, the route will not appear in the sidebar and cannot be accessed", "启用成功！": "Activated successfully!", "停用成功！": "Deactivation successfully!", "确认要启用吗？": "Are you sure you want to enable it?", "确认要停用吗？": "Are you sure you want to deactivate it?", "添加用户": "Add user", "修改用户": "Edit User", "批量取消授权": "Batch deauthorization", "取消授权": "Cancel authorization", "确认要取消该用户的角色吗？": "Are you sure you want to cancel the role of this user?", "是否取消选中用户授权数据项？": "Do you want to uncheck the user authorization data item?", "选择用户": "Select User", "请选择要分配的用户": "Please select a user to assign", "授权成功！": "Authorization successful!", "登录账号": "<PERSON><PERSON> Account", "角色信息": "Role Information", "部门状态": "Department State", "部门名称": "Department Name", "上级部门": "Superior Department", "负责人": "Responsible Person", "联系电话": "Phone", "上级部门不能为空": "Superior department cannot be empty", "部门名称不能为空": "Department name cannot be empty", "显示排序不能为空": "Display order cannot be empty", "请输入部门名称": "Please enter department name", "请输入负责人": "Please enter responsible Person", "请输入联系电话": "Please enter phone", "选择上级部门": "Select superior department", "添加部门": "Add Department", "修改部门": "Edit Department", "光储系统(并离网)": "PV energy storage system(on&off grid)", "光储变流器(并离网)": "PV+storage hybrid Inverter(on&off grid)", "电池系统": "Battery System", "光伏控制系统": "PV control systems(MPPT)", "光储变流器(纯并网)": "PV+storage hybrid Inverter(on grid)", "光储系统(纯并网)": "PV energy storage system(on grid)", "储能系统(纯并网)": "Energy storage system(on grid)", "储能变流器(纯并网)": "Energy storage converter(on grid)", "储能系统(并离网)": "Energy storage system(on&off grid)", "储能变流器(并离网)": "Energy storage converter(on&off grid)", "日期类型": "Date Type", "选择日期": "Select Date", "选择周": "Select Week", "选择月": "Select Month", "选择年": "Select Year", "导出详情": "Export Details", "电量统计报表": "electricity statistics report", "请选择日期": "Please select date", "物联网卡卡号": "IoT Card Number", "物联网卡状态": "IoT Card Status", "流量套餐": "Data Plan", "月使用流量": "Monthly Used Traffic", "月剩余流量": "Monthly Remaining Traffic", "到期时间": "Expiration Date", "充值": "Top Up", "解绑": "Unbundle", "可激活": "Can Be Activated", "已激活": "Activated", "可测试": "Testable", "已注销": "Write Off", "库存": "In Stock", "预注销": "Pre-Write-Off", "维护中": "Maintaining", "请输入物联网卡号": "Please enter iot card number", "请选择激活状态": "Please select iot card status", "请输入绑定项目": "Please enter binding item", "绑定SN": "Binding SN", "开始时间": "Start Time", "结束时间": "End Time", "设备SN": "SN", "添加SIM卡": "Add SIM Card", "总收益(元)": "Total Revenue (CNY)", "储能充电收益(元)": "Energy Storage Charging Income (CNY)", "储能放电收益(元)": "Energy Storage Discharge Income(CNY)", "电网放电收益(元)": "Grid Discharge Income(CNY)", "电网充电收益(元)": "Grid Charging Income(CNY)", "光伏发电量收益(元)": "PV Power Generation Income(CNY)", "等效植树(棵)": "Equivalent Planting (Trees)", "等效减排CO2(T)": "Equivalent CO2 reduction (T)", "更新时间": "Update Time", "合计": "Sum", "放电量(kWh)": "Discharge Capacity (kWh)", "充电量(kWh)": "Charge Capacity (kWh)", "光伏发电量(kWh)": "PV Power Generation (kWh)", "统计时间": "Statistical Time", "棵": "Trees", "方案名称": "Program Name", "电网充电": "<PERSON><PERSON>", "发电机": "Generator", "发电机充电": "Generator Charging", "电池充电功率": "Battery Charging Power", "备电保持SOC": "Standby Power Reserve SOC", "使用电网给电池充电": "Charging the battery using the grid", "电池没电时使用发电机": "Use generator when battery is low", "在发电机工作时给电池充电": "Charging the battery while the generator is working", "使用电网、油机给电池充电的功率（不限制光伏功率）": "Power to charge batteries using grid, generator (no limit on PV power)", "有电网时，电池保留SOC电量，SOC之上电量可给负载使用": "When connected to the grid, the battery retains SOC power and power above SOC is available to the loads", "使能": "Enabled", "不使能": "Disable", "请输入方案名称": "Please enter program name", "请选择电网充电": "Please select grid charging", "请选择发电机": "Please select generator", "请选择发电机充电": "Please select generator charging", "请输入电池充电功率": "Please enter battery charging power", "请输入备电保持SOC": "Please enter standby power reserve SOC", "添加方案": "Add programme", "修改方案": "Modify programme", "文件名称": "File Name", "文件类型": "File Type", "文件大小": "File Size", "版本名称": "Version Name", "文件路径": "File Path", "文件版本": "File Version", "下载": "Download", "选择文件": "Select File", "上传文件": "Upload File", "请输入文件名称": "Please enter the file name", "请选择文件版本类型": "Please select the file version type", "请先选择文件版本类型": "Please select the file version type first", "请选择文件": "Please select file", "上传失败": "Upload failed", "上传成功": "Upload Successful", "下载文件成功": "Download File Successfully", "请上传 大小不超过": "Please upload files up to", "的文件": "in size", "文件格式不正确, 请上传": "File format is not correct, please upload", "格式文件": "format files", "上传文件大小不能超过": "The size of the uploaded file cannot exceed ", "正在上传文件，请稍候": "Uploading files, please wait", "上传文件数量不能超过": "The number of uploaded files cannot exceed", "上传文件失败，请重试": "Failed to upload the file. Please try again", "正在下载文件，请稍候": "Please wait while downloading the file", "电价": "Electricity Price", "时段": "Work Shift", "请选择使能": "Please select enabled", "请选择开始时间": "Please select start time", "请选择结束时间": "Please select end time", "请输入电价": "Please enter electricity price", "请添加时段": "Please add time period", "最多只能添加12条哦": "You can only add a maximum of 12 items", "至少要有一条哦": "At least one", "添加": "Add", "功率": "Power", "请输入功率": "Please enter power", "买卖电是否同价": "Whether the trading electricity is the same price", "买电": "Buy Electricity", "卖电": "Sell Electricity", "请选择买卖电是否同价": "Please select whether the price is the same", "请输入下发参数": "Please enter delivery parameters", "指令类型": "Instruction Type", "下发参数": "Sending Parameters", "下发参数值": "Dispatch Parameter Values", "执行结果": "Result of Execution", "执行成功": "Executed Successfully", "执行失败": "Execution Failure", "已下发，等待执行": "Wait", "下发时间": "Issue Time", "完成时间": "Completion Time", "系统设置参数": "System Setup Parameters", "策略类参数设置": "Strategy Parameter Setting", "MAC参数": "MAC Parameters", "MDC参数": "MDC Parameters", "电池参数": "Battery Parameters", "设备升级": "Device Upgrade", "系统开关机": "System Switch", "收益统计报表": "\trevenue statistics report", "导出成功": "Export Successfully", "导出失败": "Export Failure", "导出中": "Wait", "下发状态": "Issue Status", "未下发": "Not Issued", "下发成功": "Issued Successfully", "下发中": "Is Being Issued", "下发失败": "Issue Failed", "该类参数从未下发": "This type of parameters has never been issued.", "参数已成功下发至设备，执行未知，请等待": "Parameters have been successfully delivered to the device. The execution is unknown, please wait.", "参数已成功下发至设备并已执行成功": "The parameters have been successfully delivered to the device and executed successfully.", "参数已成功下发至设备，设备并未执行成功。": "The parameters have been successfully delivered to the device, but the device has not executed successfully.", "运行模式": "Operation Mode", "削峰填谷": "Peak Shaving And Valley Filling", "手动模式": "Manual Mode", "后备模式": "Back-Up Mode", "防逆流使能": "Reverse Power Protection Enable", "开关机": "Power On & Off", "开机": "Power On", "关机": "Power Off", "下发": "Send", "参数已下发至设备": "Parameters have been delivered to the device", "查看执行结果": "Viewing the execution result", "星期一": "Monday", "星期二": "Tuesday", "星期三": "Wednesday", "星期四": "Thursday", "星期五": "Friday", "星期六": "Saturday", "星期天": "Sunday", "保存成功": "Save Successfully", "正在下发中，请稍后再下发": "In the process of delivery, please wait for a moment before posting.", "备电方案": "Backup Power Plan", "必选": "Required", "分时电价": "Time-Of-Use Price", "查看更多方案": "View More Programmes", "参数设置": "Parameter Setting", "MAC参数设置": "MAC Parameter Setting", "电池参数设置": "Battery Parameter Setting", "MDC参数设置": "MDC Parameter Setting", "在线升级": "Online Upgrade", "直流源电压设置": "Dc Source Voltage Setting", "电池恒流设置": "Battery Constant Current Setting", "电池恒功率设置": "Battery Constant Power Setting", "光伏限功率设置": "PV Power Limit Setting", "有功功率设置": "Active Power Setting", "无功功率设置": "Reactive Power Setting", "功率因数设置": "Power Factor Setting", "并离网设置": "On And Off Grid Setting", "SOC上限设置": "SOC Upper Limit Setting", "SOC下限设置": "SOC Lower Limit Setting", "充电限流值": "Charging Current Limit", "放电限流值": "Discharge Current Limit", "欠压保护": "Undervoltage Protection", "欠压恢复": "Undervoltage Recovery", "过压保护": "Overvoltage Protection", "过压恢复": "Overvoltage Recovery", "版本文件": "Version File", "请选择版本文件": "Please select the version file", "确定要恢复出厂设置吗？": "Sure about factory reset?", "恢复出厂设置成功，需要等 2 分钟后再查看": "Factory reset successful, please wait 2 minutes and check again.", "已取消该操作": "The operation has been canceled", "恢复出厂设置": "Factory Reset", "主机": "Master", "从机": "Slave", "筛选": "Filter", "所属时区": "Affiliated Time Zone", "资产管理": "Asset Management", "设备汇总": "<PERSON><PERSON>", "组合类型": "Combination Type", "组合序列号": "Combined Sequence Number", "组合光储系统": "Combined PV energy storage system", "组合光伏控制系统": "Combined PV control systems(MPPT)", "组合储能系统": "Combined Energy storage system", "确认": "Confirm", "类型": "Type", "等级": "Level", "项目管理": "Project Manage", "设备入库": "Equipment Storage", "登录": "Log in", "账号": "Username", "密码": "Password", "请输入账号": "Please enter username", "请输入密码": "Please enter password", "我已阅读并同意": "I have read and agreed to ", "ServiceTerms": "Service Terms", "和": "and", "PrivacyPokicy": "Privacy Pokicy", "请先勾选协议": "Please check the agreement", "登录成功": "Login Successfully", "地址": "Address", "添加时间": "Create Time", "项目信息": "Project Information", "设备列表": "Device List", "SIM汇总": "SIM Summary", "SIM信息": "SIM Information", "月总流量": "Monthly Total Traffic", "ProjectDetail": "Project Detail", "DeviceDetail": "<PERSON><PERSON>", "AlarmList": "Alarm List", "AlarmDetail": "Alarm Detail", "概括": "Generalize", "确定退出系统吗？": "Are you sure you want to exit the system?", "语言": "Language", "关于APP": "About APP", "清空缓存": "<PERSON>ache", "清空成功": "Clear Successfully", "联系我们": "Contact Us", "LotDetail": "IoT Card Detail", "加载更多": "Load More", "正在加载...": "Loading...", "到底了，没有更多了": "In the end, there is no more", "功能开发中~": "Functional development~", "您好": "Hello", "login.welcome": "Welcome to use Elecloud.", "中国站": "Chinese Server", "国际站": "International Server", "测试站": "Testing server", "多语言": "Language", "服务器": "Server", "发电机功率": "Generator Power", "不可以选择今天以后的时间": "You can't pick a time after today", "信息": "Information", "确定清空所有缓存，包括登录状态，记住密码等？": "Are you sure to clear all caches, including login status, remembering passwords, etc.?", "记住密码": "Remember Pwd", "aboutApp": "About APP", "当前版本": "Current Version", "版本更新": "Version updating", "aboutApp.filing": "ICP filing number：Yue ICP Bei 2023118278-3A", "开发中~": "Being Developed~", "alarmList": "Alarm", "请输入SN码": "SN", "暂无告警": "No Alarm", "editPwd": "Change Password", "请先同意勾选协议": "Please agree to the agreement first", "示例用户（三级）": "Example User (Level 3)", "台": "", "新": "New", "发现新版本": "New Version Found", "确认升级": "Confirm Upgrade", "取消升级": "Cancel Upgrade", "中断升级": "Interrupt Upgrade", "请求升级出错：": "Error requesting upgrade:", "打开appstore失败": "Failed to open appstore", "升级失败": "Upgrade Failure", "下载失败，网络错误": "Download failed, network error", "下载失败": "Download Failed", "已经是最新版本了": "Already the latest version", "稍后再说": "Talk Later", "暂无数据": "No Data", "电池告警": "Battery Alarm", "电池故障": "Battery Fault", "复制成功": "Copy Success", "查看详情": "Check The Details", "项目总数": "Total number of projects", "版本": "Version", "修改密码成功，请重新登录！": "Password changed successfully, please log in again!", "不记得原来的密码？": "Can't remember the original password？", "创建时间": "Create Time", "外设": "Per<PERSON>heral", "整机状态": "Machine Status", "内风机状态": "Internal Fan Status", "外风机状态": "External Fan Status", "压缩机状态": "Compressor Status", "电加热状态": "Electric Heating Status", "应急风机状态": "Emergency Fan Status", "空调开关机状态": "Air Conditioner On/Off Status", "出水温度": "Outlet Water Temperature", "出水压力": "Outlet Water Pressure", "A相电压": "A Phase Voltage", "B相电压": "B Phase Voltage", "C相电压": "C Phase Voltage", "正向有功电度": "Forward Active Energy", "反向有功电度": "Reverse Active Energy", "正向无功电度": "Forward Reactive Energy", "反向无功电度": "Reverse Reactive Energy", "尖正向有功电度": "Sharp Positive Active Power Energy", "尖反向有功电度": "Sharp Reverse Active Power Energy", "尖正向无功电度": "Sharp Forward Reactive Energy", "尖反向无功电度": "Sharp Reverse Reactive Energy", "峰正向有功电度": "Peak Positive Active Power Energy", "峰反向有功电度": "Peak Reverse Active Power Energy", "峰正向无功电度": "Peak Forward Reactive Energy", "峰反向无功电度": "Peak Reverse Reactive Energy", "平正向有功电度": "Flat Positive Active Power Energy", "平反向有功电度": "Flat Reverse Active Power Energy", "平正向无功电度": "Flat Forward Reactive Energy", "平反向无功电度": "Flat Reverse Reactive Energy", "谷正向有功电度": "Valley Positive Active Energy", "谷反向有功电度": "Valley Reverse Active Energy", "谷正向无功电度": "Valley Forward Reactive Energy", "谷反向无功电度": "Valley Reverse Reactive Energy", "负荷正向电量": "Load Forward Active Energy", "负荷反向电量": "Load Reverse Active Energy", "PCC电表": "PCC Meter", "储能电表": "Energy Storage Meter", "光伏电表": "P<PERSON>", "计量点电表": "Meter At Metering Point", "辅助用电电表": "Auxiliary Power Meter", "直流电表": "DC Meter", "电表在线状态": "Electric Meter Online Status", "AB线电压": "AB Line Voltage", "BC线电压": "BC Line Voltage", "CA线电压": "CA Line Voltage", "正向总电量": "Total Forward Power", "反向总电量": "Total Reverse Power", "未知别名": "Unknown <PERSON><PERSON>", "负载电表": "<PERSON><PERSON>", "进水温度": "Inlet Water Temperature", "进水压力": "Inlet Water Pressure", "空调模式": "Air Conditioning Mode", "内循环": "Inner Loop", "制热": "Heating", "创建人员": "Creator", "充电桩通信状态": "Charging Pile Communication Status", "充电枪个数": "Number Of Charging Guns", "充电桩状态": "Charging <PERSON>", "1#枪输出电流": "1# Gun Output Current", "1#枪输出电压": "1# Gun Output Voltage", "1#枪输出功率": "1# Gun Output Power", "1#枪当前SOC": "1# Gun Current SOC", "1#枪充电电量": "1# Gun Charging Capacity", "1#枪充电金额": "1# Gun Charge Amount", "1#枪电池最高温度": "1# Gun Battery Maximum Temperature", "1#枪充电时长": "1# Gun Charging Time", "1#枪状态": "1# Gun Status", "2#枪输出电流": "2# Gun Output Current", "2#枪输出电压": "2# Gun Output Voltage", "2#枪输出功率": "2# Gun Output Power", "2#枪当前SOC": "2# Gun Current SOC", "2#枪充电电量": "2# Gun Charging Capacity", "2#枪充电金额": "2# Gun Charge Amount", "2#枪电池最高温度": "2# Gun Battery Maximum Temperature", "2#枪充电时长": "2# Gun Charging Time", "2#枪状态": "2# Gun Status", "总输出功率": "Total Output Power", "充电桩": "Charging <PERSON>", "充电桩功率": "Charging Pile Power", "空闲": "Leisure", "插枪": "Insert The Gun", "充电等待": "Charging Waiting", "启动中": "Starting", "充电中": "Charging", "重连": "Reconnect", "结算状态": "Settlement Status", "放电中": "Discharging", "预约状态": "Appointment Status", "后台预约状态": "Backstage Appointment Status", "充电完成状态": "Charging Completion Status", "APP，预约状态": "APP Appointment Status", "试用期到，停止服务状态": "The trial period has expired and the service is stopped", "枪功率": "Gun Power", "枪": "Gun", "光储充系统(纯并网)": "PV+storage+charging system (on gird)", "光储充系统(并离网)": "PV+storage+charging system (on&off grid)", "动态扩容": "Dynamic Expansion", "光伏消纳": "PV Consumption", "systemOnOff": "System Switch", "IoOnOff": "IO Switch", "参数已成功下发至设备，设备并未执行成功": "The parameters have been successfully delivered to the device, but the device has not executed successfully.", "控制系统按其工作模式启停。": "The control system starts and stops according to its operating mode.", "设备已离线，不可下发": "The device is offline and cannot be delivered.", "mac": "MAC Parameter Setting", "功率因数设置 ": "Power Factor Setting ", "在手动模式下，控制系统输入出有功功率，正为放电，负为充电。": "In manual mode, the control system inputs active power, positive for discharge and negative for charging.", "控制系统输出无功功率正为容性，负为感性。": "The reactive power output of the control system is capacitive when positive and inductive when negative.", "调节输出的有功功率和无功功率的比值。": "Adjust the ratio of output active power to reactive power.", "在手动控制下，设置其系统是否并入电网。": "Under manual control, set whether the system is connected to the grid.", "mdc": "MDC Parameter Setting", "设置DC模块恒压模式下输出电压。": "Set the output voltage of the DC module in constant voltage mode.", "设置恒流模式下的输出电流。": "Sets the output current in constant current mode.", "设置恒功率模式下的输出功率。": "Set the output power in constant power mode.", "设置光伏功率最大值。": "Set the maximum PV power.", "bms": "Battery Parameter Setting", "设置电池停止充电时SOC。": "Set the SOC when the battery stops charging.", "设置电池停止放电时SOC。": "Set the SOC when the battery stops discharging.", "设置电池充电时的电流最大值。": "Set the maximum current when charging the battery.", "设置电池放电时的电流最大值。": "Set the maximum current when discharging the battery.", "电池放电保护电压。": "Battery discharge protection voltage.", "电池可放电恢复电压。": "The battery can be discharged to restore the voltage.", "电池充电电保护电压。": "Battery charging protection voltage.", "电池可充电恢复电压。": "The battery can be recharged to restore voltage.", "strategy": "Strategy Parameter Settings", "召测": "Call for test", "获取本地设备削峰填谷方案": "Obtain the peak load shaving and valley filling solution for local devices", "按照设置时间段给系统充放电。": "Charge and discharge the system according to the set time period.", "用于电网计算收益。": "Used for power grid calculation revenue.", "防止系统放电馈入电网。": "Prevent system discharge from feeding into the grid.", "schemeList": "Selection Scheme", "operation": "Operations", "operationManage": "Operation Management", "运维方案": "Operations Scheme", "操作日志": "Operation Log", "指令记录": "Command Record", "jfpgDetails": "Peak Shaving And Valley Filling Information", "backupDetails": "Backup Power Plan Details", "备电信息": "Backup Information", "priceDetails": "Electricity price information", "费率": "Rate", "削峰填谷信息": "Peak Shaving And Valley Filling Information", "是否使能": "Whether to enable", "jfpgList": "Peak Shaving And Valley Filling", "backupList": "Electricity Backup Program", "priceList": "Time-Of-Use Price", "请输入关键字": "Please enter keyword", "您无权限哦~": "You don't have permission.", "instructDetails": "Instruction Detail", "操作人员": "Operating Personnel", "下发远程": "Remote Delivery", "添加削峰填谷": "Add Peak Shaving And Valley Filling", "修改削峰填谷": "Modify Peak Shaving And Valley Filling", "时段信息": "Work Shift Information", "请选择是否使能": "Please select enabled", "注：功率：分正负，正为放电，负为充电": "comment: Power: divided into positive and negative, positive for discharge, negative for charging.", "添加分时电价": "Add Time-Of-Use Price", "修改分时电价": "Modify Time-Of-Use Price", "费率一": "Rate 1", "费率二": "Rate 2", "费率三": "Rate 3", "费率四": "Rate 4", "费率五": "Rate 5", "请选择费率": "Please select rate", "注：电价：电价货币为项目绑定的货币为准，如项目绑定货币为美元，那么电价为0.2美元": "comment:  Electricity price: The electricity price currency is based on the currency bound to the project. If the project is bound to the US dollar, the electricity price is 0.2 US dollars.", "添加备电方案": "Add Backup Power Plan", "修改备电方案": "Modify Backup Power Plan", "升级记录": "Upgrade Record", "远程控制记录": "Remote Control Record", "回复类型": "Reply Type", "回复结果": "<PERSON><PERSON> Result", "回复时间": "Reply Time", "升级文件": "Upgrade File", "升级版本": "upgrade Version", "当前升级个数": "Number Of Current Updates", "总升级个数": "Total Upgrades", "升级结果": "Upgrade Result", "升级描述": "Upgrade Description", "升级时间": "Upgrade Time", "成功": "Succeed", "目录创建失败": "Directory creation failure", "文件下载失败": "File download failure", "库文件缺失": "Library file missing", "question": "FAQS", "quesDetails": "Details", "页面数据加载速度过慢？": "Is page data loading slow?", "选择服务器说明？": "Select server description?", "如果手机内其他 APP 产品运行不卡顿，网络状况良好，请在“我的”页面点击清空缓存后，重新进行尝试。": "If other APP products in the phone do not run smoothly and the network is in good condition, please click on the 'My' page to clear the cache and try again.", "为了让全球各地用户有更好的使用体验，系统划分了不同地域供用户访问，地域分为：": "In order to allow users around the world to have a better experience, the system is divided into different regions for users to visit, regions are divided into:", "中国站、国际站": "China Station, International station", "（亚洲其他国家/地区、欧洲、非洲、大洋洲、北美洲、南美洲、南极洲等）。": "(Other Asian countries, Europe, Africa, Oceania, North America, South America, Antarctica, etc.).", "选择不同服务器的影响": "Impact of choosing different servers", "中国站、国际站对应不同的账号体系": "China station and international station correspond to different account systems", "如果用户想要分别访问中国大陆、国际，需要用户分别开通账号并登录使用。": "If users want to visit mainland China and internationally separately, they need to open accounts and log in separately.", "中国站、国际站的数据是隔离的": "The data of China station and international station are isolated", "即访问中国大陆地域，是无法看到国际站的数据。": "That is, if you visit the mainland China region, you cannot see the data of the international site.", "该如何选择服务器": "How do I select a server", "如果您的业务范围都在中国大陆，您选择中国站": "If your business scope is all in mainland China, you should choose China Station", "如果您的业务范围都在国际，您选择国际站": "If your business scope is international, you choose the international site", "如果您的业务范围在中国大陆、国际都存在，那您需要在中国站、国际站分别开通账号，以管理不同地域的业务": "If your business scope exists in both mainland China and internationally, you need to open accounts on the Chinese site and the international site respectively to manage business in different regions.", "业务范围是指您管理电站的所在地范围": "Business scope refers to the location of the power station you manage", "注": "comment", "其他": "Others", "常见问题": "FAQ", "如果手机内其他 APP 产品运行不卡顿，网络状况良好，请在“设置”页面点击清空缓存后，重新进行尝试。": "If other APP products in the phone are running smoothly and the network condition is good, please click Clear Cache on the 'Setting' page and try again.", "簇": "Cluster", "簇级传感器1": "Cluster Sensor 1", "簇级传感器2": "Cluster Sensor 1", "最高单体电压": "Maximum Cell Voltage", "最低单体电压": "Minimum Cell Temperature", "温升": "TR", "详情": "Details", "未知电表": "Unknown Meter", "暂未配置电池电芯规格信息，请联系管理员或售后人员。": "No battery cell specifications have been configured. Contact the administrator or after-sales personnel.", "消防": "Firefighting", "告警代码": "Alarm Point", "一月": "January", "二月": "February", "三月": "March", "四月": "April", "五月": "May", "六月": "June", "七月": "July", "八月": "August", "九月": "September", "十月": "October", "十一月": "November", "十二月": "December", "执行周期": "Execution Cycle", "每天": "Everyday", "每周（1~7天）": "Every week (1 to 7 days)", "1~12个月": "1 to 12 months", "注：该执行周期只适用于削峰填谷。": "Note: This execution cycle is only applicable for peak shaving and valley filling.", "完成": "Accomplish", "市电电表": "AC Meter", "卡号": "Card Number", "所属国家": "Affiliated Country", "countryList": "select country", "请输入序列号": "Please enter serial number", "请输入装机容量": "Please enter installed capacity", "组合设备信息": "Combined device information", "选择类型": "Select Type", "运维": "Operations", "经度": "Longitude", "纬度": "Latitude", "货币": "<PERSON><PERSON><PERSON><PERSON>", "选择国家": "Select Country", "选择时区": "Select Time Zone", "选择货币": "Select Currency", "请输入ICCID": "Please enter ICCID", "长度为20": "Length 20", "分配": "Allot", "分配项目": "Allocate Project", "确定分配": "Confirm Allocate", "注：分配项目，会把项目所绑定的设备也一起被分配给用户。": "Note: When you assign a project, the devices bound to the project will also be assigned to the user.", "请选择所要分配的用户": "please select user to be assigned", "分配失败": "Allocation Failure", "分配成功": "Allocation Successful", "该国家没有数据": "There is no data available for this country.", "分配SIM卡": "Allocate SIM Cards", "APP更新后页面卡住？": "The page is stuck after the APP update?", "请退出登录重新登录": "Please log out and log in again", "点击`设置`-`清空缓存`": "<PERSON><PERSON> Settings - <PERSON>.", "如若还不能访问，请联系管理员点击`设置`-`联系我们`": "If you still cannot access it, please contact the administrator and click `Settings`-`Contact Us`", "切换语言成功": "Language Switching Successful", "切换服务成功": "Switching Service Successfully", "选择语言": "Select Language", "选择服务器": "Select Server", "没有更多了": "No more", "包": "Pack", "正母线电压": "Positive Bus Voltage", "负母线电压": "Negative Bus Voltage", "是否显示柴油机": "Whether to display diesel engine", "BMS类型": "BMS Type", "宁德": "CATE", "协能": "协能", "高泰昊能": "Qualtech", "华塑": "<PERSON><PERSON><PERSON>", "高特": "GOLD ELECTRONIC", "华思": "HUASI", "小鸟": "<PERSON>", "山东威马": "山东威马", "亿纬锂电": "EVE Energy", "力神": " Lishen", "帷幕-BCU": "帷幕-BCU", "宁德液冷": "宁德液冷", "三级宁德": "三级宁德", "优旦": "<PERSON><PERSON>", "欣旺达": "Sunwoda", "沛城电子": "Pace Electronics", "帷幕-BCU2": "帷幕-BCU2", "群控能源GCE": "群控能源GCE", "高特三级bms": "高特三级bms", "科工": "科工", "采集屏版本": "Capture Screen Version", "采集屏": "Capture Screen", "需要升级HMI跟模块": "Need to upgrade HMI and modules", "只需要升级HMI": "Only need to upgrade HMI", "采集屏跟云平台通讯": "The collection screen communicates with the cloud platform", "主屏版本": "Main Screen Version", "副屏版本": "Secondary Screen Version", "时区管理": "Time Zone Management", "货币管理": "Currency Management", "描述": "Description", "添加时区": "Add Time Zone", "请输入时区": "Please enter time zone", "修改时区": "Modify time zone", "货币单位": "Currency Unit", "添加货币": "Add currency", "修改货币": "Modify currency", "请输入国家": "Please country", "请输入货币单位": "Please enter currency unit", "告警详情": "Alarm Details", "告警信息": "Alarm Information", "问题分析": "Problem Analysis", "问题原因": "Cause", "处理办法": "Solution", "涉及部件": "Parts Involved", "暂无详细信息": "No details availableNo detailed information yet", "格式不正确": "Incorrect format", "远程控制": "Remote Control", "变压器容量": "Transformer Capacity", "变压器容量。": "Transformer Capacity.", "设备离线，不可操作": "The device is offline and cannot be operated.", "remoteControl": "Remote Control", "controlInfo": "Remote control information", "关闭远程控制": "Turn off remote control", "开启远程控制": "Enable remote control", "开启密码": "Open Password", "VNC模式": "VNC Mode", "SSH端口": "SSH Port", "VNC端口": "VNC Port", "VNC和SSH的端口不能重复": "The VNC and SSH ports must be unique", "开启SSH": "Enable SSH", "开启SSH+VNC": "Enable SSH+VNC", "开启VNC": "Enable VNC", "该设备已开启远程控制，是否需要关闭远程控制": "Remote control is enabled on the device. Determine whether to disable remote control.", "正在关闭中": "Under closure", "关闭远程成功": "Close remote successfully", "确认关闭": "Confirm Close", "查看端口": "View Port", "开启内网VNC": "Enabling Intranet VNC", "注：开启内网VNC的端口为5900。": "Note: The port for enabling Intranet VNC is 5900.", "aboutApp.name": "亿兰科云平台（Elecloud）", "不允许有中文字符": "Chinese characters are not allowed", "MDC直流源": "MDC DC Source", "正向电量": "Forward Power", "反向电量": "Reverse Power", "直流源功率": "DC source power", "请输入正确的邮箱": "Please enter a valid email address", "公告": "Notice", "公告通知": "Notice", "noticeDetail": "Notice Detail", "未读": "Unread", "已读": "Read", "全部已读": "<PERSON>", "没有未读公告": "No Unread Notices", "暂无公告": "No Notices", "发布时间": "Published Time", "发布人": "Publisher", "系统管理员": "System Administrator", "暂未提供": "Not Available Yet", "时间降序": "Time Desc", "时间升序": "Time Asc", "状态降序": "Status Desc", "状态升序": "Status Asc", "收起": "Collapse", "展开": "Expand", "upgrade": "Online Upgrade", "请到微信公众号进行充值，微信公众号为：": "Please recharge via WeChat official account:", "深圳": "Shenzhen", "新加坡": "Singapore", "访问域名": "Access Domain Name", "失败是否重启": "Restart if failed", "升级进度": "Upgrade Progress", "正在下载远程升级包": "Downloading the remote upgrade package", "远程升级包下载成功": "The remote upgrade package is successfully downloaded", "远程升级包下载失败": "The remote upgrade package fails to be downloaded", "升级成功": "Update Successfully", "升级中": "Upgrading", "升级类型": "Upgrade Type", "主屏": "Home Screen", "副屏": "Secondary Screen", "升级对象": "Upgrade object", "TAR版本文件": "TAR Version File", "INI版本文件": "INI Version File", "模块": "<PERSON><PERSON><PERSON>", "versionFileList": "Select File", "HMI升级文件": "HMI Upgrade File", "MAC升级文件": "MAC Upgrade File", "MDC升级文件": "MDC Upgrade File", "STS升级文件": "STS Upgrade File", "BMS升级文件": "BMS Upgrade File", "充放次数": "Charge and Discharge Times", "incomeList": "Benefit List", "公司名称": "Company Name"}