/**
 *  RT01 光储系统(并离网)协议版本:风冷电池VB03液冷电池VB02
    RT02 光储变流器(并离网)
    RT03 电池系统
    RT04 光伏控制系统
    RT05 光储变流器(纯并网)
    RT06 光储系统(纯并网)风冷电池 VB03 液冷电池VB02
    RT07 储能系统(纯并网)风冷电池 VB03 液冷电池VB02
    RTO8 储能变流器(纯并网)
    RT09 储能系统(并离网)风冷电池 VB03 液冷电池VB02
    RT10 储能变流器(并离网)
    RT11 光储充系统(纯并网)风冷电池VB03液冷电池VB02
    RT12 光储充系统(并离网)1风冷电池 VB03 液冷电池VB02
 */

export const deviceTypeSingleOptions = [
  {
    value: 1,
    label: '光储系统(并离网)',
    type: 'RT01'
  }, {
    value: 2,
    label: '光储变流器(并离网)',
    type: 'RT02'
  }, {
    value: 3,
    label: '电池系统',
    type: 'RT03'
  },
  {
    value: 4,
    label: '光伏控制系统',
    type: 'RT04'
  },
  {
    value: 5,
    label: '光储变流器(纯并网)',
    type: 'RT05'
  },
  {
    value: 6,
    label: '光储系统(纯并网)',
    type: 'RT06'
  },
  {
    value: 7,
    label: '储能系统(纯并网)',
    type: 'RT07'
  },
  {
    value: 8,
    label: '储能变流器(纯并网)',
    type: 'RT08'
  },
  {
    value: 9,
    label: '储能系统(并离网)',
    type: 'RT09'
  },
  {
    value: 10,
    label: '储能变流器(并离网)',
    type: 'RT10'
  },
  {
    value: 11,
    label: '光储充系统(纯并网)',
    type: 'RT11'
  },
  {
    value: 12,
    label: '光储充系统(并离网)',
    type: 'RT12'
  },
  {
    value: 13,
    label: 'MDC直流源',
    type: 'RT13'
  },
]

/**
 *  10000 组合光储系统
    10001 组合光伏控制系统
    10002 组合储能系统
 */
export const deviceTypeGroupOptions = [{
  value: 10000,
  label: '组合光储系统',
  type: 'RT10000'
},
{
  value: 10001,
  label: '组合光伏控制系统',
  type: 'RT10001'
},
{
  value: 10002,
  label: '组合储能系统',
  type: 'RT10002'
}
]

/**
 * ems
 */
export const deviceTypeEmsOptions = [
  {
    value: 99999,
    label: 'EMS',
    type: 'RT99999'
  }
]

/**
 * 是否为组合设备类型
 */
export const isGroupFn = (type) => {
  return deviceTypeGroupOptions.map(item => {
    // item.label = uni.$t(item.label)
    return item
  }).some(item => item.value == type)
}

/**
 * 是否为ems设备类型
 */
export const isEmsFn = (type) => deviceTypeEmsOptions.some(item => item.value == type)

/**
 * 电网功率，有sts时1031~1033（并离网），无1051（纯并网）
 */
export const isPowerFn = (type) => type == 1 || type == 2 || type == 9 || type == 10 || type == 12

/**
 * 有光伏的
 */
export const isPhotovoltaicFn = (type) => type == 1 || type == 2 || type == 4 || type == 5 || type == 6 || type == 11 || type == 12 || type == 10000 || type == 10001

/**
 * 有储能的
 */
export const isEnergyFn = (type) => type == 1 || type == 2 || type == 3 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10 || type == 11 || type == 12 || type == 10000 || type == 10002

/**
 * 有电网的
 */
export const isGirdFn = (type) => type == 1 || type == 2 || type == 5 || type == 6 || type == 7 || type == 8 || type == 9 || type == 10 || type == 11 || type == 12 || type == 10000 || type == 10002

/**
 * 有电池的
 */
export const isCellFn = (type) => type == 1 || type == 3 || type == 6 || type == 7 || type == 9 || type == 11 || type == 12 || type == 10000 || type == 10002

/**
 * 直流母线功率，储能变流器，用1056，不是用1077
 */
export const isBusFn = (type) => type == 8 || type == 10
export const isBusFn2 = (type) => type == 2 || type == 3 || type == 4 || type == 5 || type == 8 || type == 10

/**
 * 是否为系统
 */
export const isConverterFn = (type) => type == 1 || type == 3 || type == 4 || type == 6 || type == 7 || type == 9 || type == 11 || type == 12 || type == 10000 || type == 10001 || type == 10002

/**
 * 获取设备类型
 */
export const getDeviceType = (type, needGroup, needEms = true, showLabel = true) => {
  if (!type) return
  let options = []
  let groupData = deviceTypeGroupOptions.map(item => {
    // item.label = uni.$t(item.label)
    return item
  })
  let singData = deviceTypeSingleOptions.map(item => {
    // item.label = uni.$t(item.label)
    return item
  })
  options = needGroup ? [...singData, ...groupData] : singData
  options = needEms ? [...options, ...deviceTypeEmsOptions] : options
  let value = options.find(item => item.value == type)
  if (showLabel) {
    return value?.label ?? ''
  } else {
    return value?.type ?? ''
  }
}