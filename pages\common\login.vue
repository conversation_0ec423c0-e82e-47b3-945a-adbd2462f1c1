<template>
  <view class="login">
    <view class="login-top">
      <u-avatar :src="currentLanguage == '简体中文' ? appIcon: appIconEn" size="140"></u-avatar>
      <view class="u-m-t-20">{{ $t('login.welcome') }}</view>
    </view>
    <view class="login-box">
      <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" :labelStyle="formLabelStyle" errorType="toast">
        <u-form-item prop="username" leftIcon="../../static/login-user.png">
          <u--input v-model="form.username" border="none" :placeholder="$t('请输入账号')" :adjustPosition="false"
            :placeholderStyle="`{
            color: ${otherColor.loginPlaceholderColor}
          }`" :color="otherColor.loginInputColor"></u--input>
        </u-form-item>
        <u-form-item prop="password" leftIcon="../../static/login-pwd.png">
          <u-input v-model="form.password" :password="isPwd" border="none" :placeholder="$t('请输入密码')"
            :adjustPosition="false" :placeholderStyle="`{
            color: ${otherColor.loginPlaceholderColor}
          }`" :color="otherColor.loginInputColor">
            <template #suffix>
              <image src="../../static/login-close.png" style="width: 32rpx;height: 32rpx"
                @click="handleShowPwd('show')" v-if="isPwd"></image>
              <image src="../../static/login-eye.png" style="width: 32rpx;height: 32rpx" @click="handleShowPwd('hide')"
                v-else></image>
            </template>
          </u-input>
        </u-form-item>
      </u-form>
      <!-- 记住密码 -->
      <view class="u-m-t-15 u-p-l-40 u-p-r-40 flex u-flex-between align-items">
        <u-checkbox-group v-model="rememberValue" size="28" iconSize="14px" class="align-items">
          <u-checkbox shape="circle" :name="true" :label="$t('记住密码')" labelSize="14px"
            :labelColor="otherColor.loginColor" :activeColor="otherColor.primaryColor"></u-checkbox>
        </u-checkbox-group>
        <view class="ft12 color-grey" @click="handleDemoClick">{{ $t('示例用户（三级）') }}</view>
      </view>
      <u-button :text="$t('登录')" type="primary" :throttleTime="1000" shape="circle" class="u-m-t-40"
        @click="handleLoginClick" style="height: 90rpx"></u-button>
    </view>
    <!-- 隐私协议 -->
    <u-checkbox-group v-model="value" size="28" iconSize="14px" class="u-m-t-15 login-footer align-items">
      <u-checkbox shape="circle" :name="true" :activeColor="otherColor.primaryColor"></u-checkbox>
      <view class="flex">
        <span @click="value[0] = !value[0]">{{ $t('我已阅读并同意') }}</span>&nbsp;<u-text type="primary" size="12px"
          :text="$t('ServiceTerms')" decoration="underline" @click="handleServiceClick"
          style="flex: none;width: auto;"></u-text>&nbsp;<span
          @click="value[0] = !value[0]">{{ $t('和') }}</span>&nbsp;<u-text type="primary" size="12px"
          :text="$t('PrivacyPokicy')" decoration="underline" @click="handlePrivacyClick"
          style="flex: none;width: auto;"></u-text>
      </view>
    </u-checkbox-group>

    <!-- 工具栏 -->
    <DataSelect :clear="false" :localdata="options" @change="handleData($event)" class="top-tool">
      <u-icon name="more-dot-fill" size="20px" color="#333"></u-icon>
    </DataSelect>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
  } from 'vue';
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import otherColor from '../../common/other.module.scss'
  import { formLabelStyle } from '@/constant'
  import { languageArr } from '@/locale'

  import appIcon from '@/static/appIcon.png'
  import appIconEn from '@/static/appIconEn.png'
  import DataSelect from '@/components/data-select/index.vue'

  const loginStore = useLoginStore()
  const {
    proxy
  } = getCurrentInstance()

  // 使用 reactive 创建响应式状态  
  const form = ref({
    password: '',
    username: ''
  })
  const rules = ref({
    username: {
      type: 'string',
      required: true,
      message: uni.$t('请输入账号'),
      trigger: ['blur', 'change'],
    },
    password: {
      type: 'string',
      required: true,
      message: uni.$t('请输入密码'),
      trigger: ['blur', 'change'],
    },
  })
  // 使用 ref 创建响应式引用  
  const formRef = ref(null);
  const value = ref([false])

  const handleLoginClick = () => {
    if (!value.value[0]) return uni.$u.toast(uni.$t('请先同意勾选协议'))
    if (formRef.value) {
      formRef.value.validate().then(async valid => {
        if (valid) {
          await loginStore.Login({
            username: uni.$u.trim(form.value.username),
            password: form.value.password
          })
          if (rememberValue.value[0]) { // 记住密码
            uni.cache.setItem('username', form.value.username)
            uni.cache.setItem('password', form.value.password)
            uni.cache.setItem('rememberValue', true)
            uni.cache.setItem('agree', true)
          }
          uni.$u.toast(uni.$t('登录成功'))
          uni.switchTab({
            url: '/pages/index/index'
          })
        }
      })
    }
  }
  /**
   * 是否显示密码
   */
  const isPwd = ref(true)
  const handleShowPwd = (type) => {
    isPwd.value = type == 'show' ? false : true
  }
  /**
   * 记住密码
   */
  const rememberValue = ref([false])
  onShow(() => {
    const username = uni.cache.getItem('username')
    const password = uni.cache.getItem('password')
    const rememberValueS = uni.cache.getItem('rememberValue')
    const agree = uni.cache.getItem('agree')
    if (username && password && rememberValue) {
      form.value = {
        ...form.value,
        username,
        password
      }
    }
    rememberValue.value = [rememberValueS]
    value.value = [agree]
  })

  /**
   * 隐私协议
   */
  const handlePrivacyClick = () => {
    let lang = uni.cache.getItem('language')
    if (lang == 'zh-Hans') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy'
      })
    } else if (lang == 'en') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy-en'
      })
    } else if (lang == 'it') {
      uni.navigateTo({
        url: '/pages/common/privacy-policy-it'
      })
    }
  }
  const handleServiceClick = (type) => {
    let lang = uni.cache.getItem('language')
    if (lang == 'zh-Hans') {
      uni.navigateTo({
        url: '/pages/common/service-terms'
      })
    } else if (lang == 'en') {
      uni.navigateTo({
        url: '/pages/common/service-terms-en'
      })
    } else if (lang == 'it') {
      uni.navigateTo({
        url: '/pages/common/service-terms-it'
      })
    }
  }

  /**
   * 下拉菜单
   */
  /**
   * 切换语言
   */
  const serviceArr = ref([])
  const options = ref([{
      text: uni.$t('多语言'),
      value: 'lang',
      icon: 'login-lang'
    },
    {
      text: uni.$t('服务器'),
      value: 'service',
      icon: 'login-service'
    }
  ])
  const initLanguage = () => {
    serviceArr.value = JSON.parse(import.meta.env.VITE_SERVICE_ARR).map(item => {
      if (item.value == 'zh' || item.value == 'en' || item.value == 'test') {
        item.label = uni.$t(item.label)
      }
      return item
    })
    if (serviceArr.value.length > 1) {
      options.value = [{
          text: uni.$t('多语言'),
          value: 'lang',
          icon: 'login-lang'
        },
        {
          text: uni.$t('服务器'),
          value: 'service',
          icon: 'login-service'
        }
      ]
    } else {
      options.value = [{
        text: uni.$t('多语言'),
        value: 'lang',
        icon: 'login-lang'
      }, ]
    }
  }
  initLanguage()
  const actionName = ref()
  const handleData = (action) => {
    actionName.value = action
    // 忽略初始化时的传入的空操作
    if (action === '') {
      return
    }

    uni.navigateTo({
      url: `/pages/common/selectTool?actionName=${action}`
    })
  }
  uni.$on('toolClick', (data) => {
    if (!data) return
    if (actionName.value == 'lang') initLanguage()
  })
  onUnload(() => {
    uni.$off('toolClick')
  })

  /**
   * 实例用户（三级）
   */
  const handleDemoClick = async () => {
    await loginStore.Login({
      username: 'demo01',
      password: 'demo01'
    })
    await loginStore.GetInfo()
    uni.switchTab({
      url: '/pages/index/index'
    })
  }
  
  const currentLanguage = ref()
  currentLanguage.value = languageArr.find(item => item.value == uni.cache.getItem('language')).label
</script>

<style scoped lang="scss">
  .login {
    width: 100%;
    height: 100vh;
    position: relative;
    /* background-color: $uni-bg-color; */

    .login-top {
      height: 500rpx;
      background: $transparent;
      /* padding: 200rpx 40rpx 0; */
      font-size: 16px;
      line-height: 70rpx;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      padding-bottom: 40rpx;
    }

    .login-box {
      width: 100%;
      /* background-color: $uni-bg-color; */
      position: absolute;
      top: 480rpx;
      border-top-left-radius: 60rpx;
      border-top-right-radius: 60rpx;
      padding: 0 40rpx;
      padding-top: 40rpx;
    }

    .login-footer {
      position: absolute;
      bottom: 80rpx;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: $uni-text-color-grey;
    }

    .top-tool {
      position: fixed;
      top: 50px;
      right: 30px;
    }
  }

  :deep(.u-input) {
    padding: 8rpx 0 !important;
  }

  :deep(.u-form-item) {
    background: #fff;
    border-radius: 100rpx;
    padding: 0 40rpx;
    margin-bottom: 40rpx;
  }
</style>