// 获取列表
export const backupList = (queryInfo) => uni.$u.http.get('/system/reservePattern/list', {
  params: queryInfo
})

// 获取全部列表
export const backupAll = (queryInfo) => uni.$u.http.get('/system/reservePattern/list2', {
  params: queryInfo
})

// 新增
export const addBackup = (data) => uni.$u.http.post('/system/reservePattern', data)

// 修改
export const editBackup = (data) => uni.$u.http.put('/system/reservePattern', data)

// 删除
export const deleteBackup = (queryInfo) => uni.$u.http.delete(`/system/reservePattern/${queryInfo.ids}`)

// 查看
export const lookBackup = (queryInfo) => uni.$u.http.get(`/system/reservePattern/${queryInfo.id}`)
