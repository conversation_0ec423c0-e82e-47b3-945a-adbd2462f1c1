<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../../static/detail.png" class="item-img"></image>
          <span>{{ $t('SIM信息') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('物联网卡卡号') }}</view>
        <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
          <view class="u-m-r-5">{{ info.sim }}</view>
          <u-copy :content="info.sim" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
            <image src="../../../static/copy.png" class="copy-img-width"></image>
          </u-copy>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('物联网卡状态') }}</view>
        <view><u-tag :text="getStatusCom().label" size="mini" :type="getStatusCom().type" plain
                plainFill></u-tag>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('流量套餐') }}</view>
        <view>{{ info.packageName }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('月总流量') }}</view>
        <view>{{ info.totalFlow }}
        <span class="item-unit">MB</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('月使用流量') }}</view>
        <view>{{ info.usedFlow }}
          <span class="item-unit">MB</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('月剩余流量') }}</view>
        <view>{{ info.residualFlow || '--' }}
          <span class="item-unit">MB</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ 'SN' }}</view>
        <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
          <view class="u-m-r-5">{{ info.ac }}</view>
          <u-copy :content="info.ac" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
            <image src="../../../static/copy.png" class="copy-img-width"></image>
          </u-copy>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('创建人员') }}</view>
        <view>{{ info.createBy }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('开始时间') }}</view>
        <view>{{ info.startTime }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('到期时间') }}</view>
        <view>{{ info.endTime }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { onLoad, onShow } from '@dcloudio/uni-app'
  import { getSimInfo } from '@/api/sim.js'
  
  const id = ref()
  onLoad((options) => {
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })
  
  const options = ref([{
      value: undefined,
      label: uni.$i18n().t('全部')
    },
    {
      value: 2,
      label: uni.$i18n().t('已激活')
    },
    {
      value: 3,
      label: uni.$i18n().t('停机')
    },
    {
      value: 7,
      label: uni.$i18n().t('已注销')
    },
    {
      value: 1,
      label: uni.$i18n().t('可激活')
    },
    {
      value: 6,
      label: uni.$i18n().t('可测试')
    },
    {
      value: 11,
      label: uni.$i18n().t('库存')
    },
    {
      value: 15,
      label: uni.$i18n().t('预注销')
    },
    {
      value: 88,
      label: uni.$i18n().t('维护中')
    },
  ])
  
  const info = ref({})
  const getInfoFn = async () => {
    const res = await getSimInfo({ id: id.value })
    info.value = res.data
  }
  const getStatusCom = computed(() => {
    return () => {
      let label = options.value.find(item => item.value == info.value.status).label
      let type = ''
      if (info.value.status == 2) {
        type = 'success'
      } else if (info.value.status == 3) {
        type = 'warning'
      } else if (info.value.status == 7) {
        type = 'error'
      } else {
        type = ''
      }
      return {
        label,
        type
      }
    }
  })
  
  const isAndroid = ref(uni.$u.sys().platform == 'android')
</script>

<style lang="scss" scoped>
  .device-info {
    height: 100vh;
    overflow: auto;
    .base-info, .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 20rpx 30rpx;
    }
  
    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;
      
      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }
  
      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }
  
      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }
  
    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>
