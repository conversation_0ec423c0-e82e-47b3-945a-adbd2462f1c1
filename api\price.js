// 获取列表
export const priceList = (queryInfo) => uni.$u.http.get('/system/electricPrice/list', {
  params: queryInfo
})

// 新增
export const addPrice = (data) => uni.$u.http.post('/system/electricPrice', data)

// 修改
export const editPrice = (data) => uni.$u.http.put('/system/electricPrice', data)

// 获取全部数据
export const allPrice = (queryInfo) => uni.$u.http.get('/system/electricPrice/getElectricPrices', {
  params: queryInfo
})

// 删除
export const deletePrice = (queryInfo) => uni.$u.http.delete(`/system/electricPrice/${queryInfo.electricIds}`)

// 查看
export const lookPrice = (queryInfo) => uni.$u.http.get(`/system/electricPrice/${queryInfo.id}`)
