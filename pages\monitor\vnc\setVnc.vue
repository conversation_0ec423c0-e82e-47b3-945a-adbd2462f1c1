<template>
  <view class="add-box">
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('开启密码')" prop="pwd" :borderBottom="true" required>
          <u-input v-model="form.pwd" type="password" :placeholder="$t('请输入')" border="none" />
        </u-form-item>
        <u-form-item :label="$t('VNC模式')" prop="vncMode" :borderBottom="true" @click="handlePortCell('mode')" required>
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ vncModeText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('VNC端口')" prop="vncPortId" :borderBottom="true" @click="handlePortCell('vnc')"
          v-if="form.vncMode == 5 || form.vncMode == 3" required>
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ vncPortText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('SSH端口')" prop="sshPortId" :borderBottom="true" @click="handlePortCell('ssh')"
          v-if="form.vncMode == 1 || form.vncMode == 3" required>
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ sshPortText }}
          </view>
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%;text-align: center;" v-if="form.vncMode == 7">
      {{ $t('注：开启内网VNC的端口为5900。') }}
    </view>


    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="[modeOptions]"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    listAll,
    argumentsJsonFrpOpen
  } from '@/api/monitor'
  import {
    checkRole
  } from '@/common/utils.js'

  onLoad((options) => {
    form.value = {
      ...form.value,
      ...options
    }
  })

  const formRef = ref(null)
  const form = ref({
    ac: '',
    id: '',
    pwd: '',
    sshPortId: '',
    vncMode: 5,
    vncPortId: ''
  })
  const rules = ref({
    'pwd': {
      type: 'string',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'sshPortId': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'vncMode': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'vncPortId': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
  })

  const vncModeText = ref(uni.$t('请选择'))
  const vncPortText = ref(uni.$t('请选择'))
  const sshPortText = ref(uni.$t('请选择'))
  const selectType = ref()
  const handlePortCell = (type) => {
    selectType.value = type
    if (type == 'vnc') {
      uni.navigateTo({
        url: `/pages/monitor/vnc/portList?id=${form.value.vncPortId}&type=${type}`
      })
    } else if (type == 'ssh') {
      uni.navigateTo({
        url: `/pages/monitor/vnc/portList?id=${form.value.sshPortId}&type=${type}`
      })
    } else if (type == 'mode') {
      isShowSelect.value = true
    }
  }
  uni.$on('portItemClick', (data) => {
    if (!data) return
    if (selectType.value == 'vnc') {
      form.value.vncPortId = data.id
      vncPortText.value = data.port
    } else if (selectType.value == 'ssh') {
      form.value.sshPortId = data.id
      sshPortText.value = data.port
    }
  })
  onUnload(() => {
    uni.$off('portItemClick')
  })
  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          uni.showLoading({
            title: `${uni.$t('正在下发中')}...`
          })
          argumentsJsonFrpOpen({
            ac: form.value.ac,
            id: form.value.id,
            pwd: form.value.pwd,
            sshPortId: form.value.sshPortId,
            vncMode: form.value.vncMode,
            vncPortId: form.value.vncPortId,
          }).then(response => {
            if (response.code !== 200) {
              uni.hideLoading()
              return uni.$u.toast(uni.$u('下发失败'))
            }
            uni.hideLoading()
            uni.navigateBack()
            uni.$u.toast(uni.$u('下发成功'))
          }).catch(() => {
            uni.hideLoading()
          })
        }
      })
    }
  }

  /**
   * 选择
   */
  onShow(() => {
    if (checkRole(['admin'])) {
      modeOptions.value = [{
          text: uni.$t('开启VNC'),
          value: 5
        },
        {
          text: uni.$t('开启SSH'),
          value: 1
        },
        {
          text: uni.$t('开启SSH+VNC'),
          value: 3
        },
        {
          text: uni.$t('开启内网VNC'),
          value: 7
        }
      ]
    } else {
      modeOptions.value = [{
          text: uni.$t('开启VNC'),
          value: 5
        },
        {
          text: uni.$t('开启内网VNC'),
          value: 7
        }
      ]
    }
    
    vncModeText.value = modeOptions.value.find(item => item.value == form.value.vncMode).text
  })
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const modeOptions = ref([])
  const slectModeText = computed(() => {
    return modeOptions.value.find(item => item.value == form.value.vncMode)?.text
  })
  const handleSelectConfirm = ({
    value
  }) => {
    form.value.vncMode = value[0].value
    vncModeText.value = value[0].text
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    isShowSelect.value = false
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>