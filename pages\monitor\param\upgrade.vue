<template>
  <view class="device-details">
    <z-paging ref="paging" refresher-only @onRefresh="onRefresh">
      <!-- <template #top>
      <u-tabs :list="tabListCom" :scrollable="isScrollable" :current="current" @click="handleTabClick" lineHeight="4px" :itemStyle="{
                lineHeight: '44px'
              }" :activeStyle="{
            color: otherColor.mainColor,
        }"
        :inactiveStyle="{
            color: otherColor.mainColor,
        }" :lineColor="otherColor.primaryColor" style="background-color: #fff;"></u-tabs>
    </template>
    <view>
      <component :is="currentValue.com" v-model="currentIndex"></component>
    </view> -->
      <HMIVue v-model="currentIndex"></HMIVue>
    </z-paging>
  </view>
</template>

<script setup>
  import {
    computed,
    ref,
    toRefs,
    defineAsyncComponent,
    getCurrentInstance,
    watch
  } from 'vue'
  import {
    $uGetRect,
    checkRole,
    isShowPerm
  } from '@/common/utils.js'
  import {
    onLoad,
    onReady,
    onResize,
    onUnload,
    onShow
  } from '@dcloudio/uni-app'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    storeToRefs
  } from 'pinia'
  import otherColor from '../../../common/other.module.scss'
  import { isGroupFn } from '@/hook/useDeviceType.js'
  import { isEmpty } from 'lodash'
  
  import HMIVue from '../upgrade/HMI.vue'
  import MACVue from '../upgrade/MAC.vue'
  import MDCVue from '../upgrade/MDC.vue'
  import STSVue from '../upgrade/STS.vue'
  import BMSVue from '../upgrade/BMS.vue'
  
  const { proxy } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const {
    routeQuery,
    control,
    groupControl,
    orientation,
    lineGroupQueryInfo
  } = storeToRefs(monitorStore)
  const paramStore = useParamStore()
  
  /**
   * 路由参数、初始化
   */
  const currentIndex = ref()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  onLoad((options) => {
    currentIndex.value = options.index
    getData()
  })

  const getData = () => {
    paramStore.upgradeInfoFn({
      ac: isGroup.value ? routeQuery.value.groupId[currentIndex.value]: routeQuery.value.id
    }).then(res => {
      if (!res) return
      if (res.fileType == '2') {
        current.value = 0
        currentValue.value = {
          name: 'HMI',
          com: HMIVue
        }
      } else if (res.fileType == '3') {
        current.value = 1
        currentValue.value = {
          name: 'MAC',
          com: MACVue
        }
      } else if (res.fileType == '4') {
        current.value = 2
        currentValue.value = {
          name: 'MDC',
          com: MDCVue
        }
      } else if (res.fileType == '5') {
        current.value = 3
        currentValue.value = {
          name: 'STS',
          com: STSVue
        }
      } else if (res.fileType == '6') {
        current.value = 4
        currentValue.value = {
          name: 'BMS',
          com: BMSVue
        }
      }
    })
  }

  const isScrollable = ref(false)
  const tabListCom = computed(() => {
    let data = [
      {
        name: 'HMI',
        com: HMIVue
      },
      {
        name: 'MAC',
        com: MACVue
      },
      {
        name: 'MDC',
        com: MDCVue
      },
      {
        name: 'STS',
        com: STSVue
      },
      {
        name: 'BMS',
        com: BMSVue
      },
    ]
    return data
  })
  const current = ref(0)
  const currentValue = ref({
    name: 'HMI',
    com: HMIVue
  })
  const handleTabClick = (item) => {
    current.value = item.index
    currentValue.value = item
  }
  
  const onRefresh = async () => {
    getData()
    proxy.$refs.paging.complete()
  }
</script>

<style scoped lang="scss">
  .device-details {
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }
</style>