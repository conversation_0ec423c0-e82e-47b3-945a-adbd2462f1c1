{
  "easycom": {
    "autoscan": true,
    // 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
    "custom": {
      "^u--(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "@/uni_modules/uview-plus/components/u-$1/u-$1.vue"
    }
  },
  "pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    // 概括
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "项目概括",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/common/login",
      "style": {
        "navigationStyle": "custom"
      }
    },
    // 设置
    {
      "path": "pages/profile/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    // 资产
    {
      "path": "pages/property/index",
      "style": {
        "navigationBarTitleText": "个人资产",
        "navigationStyle": "custom"
      }
    },
    // 设备监控
    {
      "path": "pages/monitor/index",
      "style": {
        "navigationBarTitleText": "设备监控",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/monitor/deviceDetails",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/common/webview",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/project/project-detail",
      "style": {
        "navigationBarTitleText": "%ProjectDetail%"
      }
    },
    {
      "path": "pages/property/device/device-detail",
      "style": {
        "navigationBarTitleText": "%DeviceDetail%"
      }
    },
    {
      "path": "pages/property/sim/sim-detail",
      "style": {
        "navigationBarTitleText": "%LotDetail%"
      }
    },
    {
      "path": "pages/common/alarm-detail",
      "style": {
        "navigationBarTitleText": "%AlarmDetail%"
      }
    },
    {
      "path": "pages/common/privacy-policy",
      "style": {
        "navigationBarTitleText": "%PrivacyPokicy%"
      }
    },
    {
      "path": "pages/common/privacy-policy-en",
      "style": {
        "navigationBarTitleText": "%PrivacyPokicy%"
      }
    },
    {
      "path": "pages/common/privacy-policy-it",
      "style": {
        "navigationBarTitleText": "%PrivacyPokicy%"
      }
    },
    {
      "path": "pages/common/service-terms",
      "style": {
        "navigationBarTitleText": "%ServiceTerms%"
      }
    },
    {
      "path": "pages/common/service-terms-en",
      "style": {
        "navigationBarTitleText": "%ServiceTerms%"
      }
    },
    {
      "path": "pages/common/service-terms-it",
      "style": {
        "navigationBarTitleText": "%ServiceTerms%"
      }
    },
    {
      "path": "pages/profile/about-app",
      "style": {
        "navigationBarTitleText": "%aboutApp%",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/alarm-list",
      "style": {
        "navigationBarTitleText": "%alarmList%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/profile/editPwd",
      "style": {
        "navigationBarTitleText": "%editPwd%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/charts/landscape",
      "style": {
        "enablePullDownRefresh": false,
        "pageOrientation": "auto",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/profile/userInfo",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/monitor/param/IoOnOff",
      "style": {
        "navigationBarTitleText": "%IoOnOff%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/onOff",
      "style": {
        "navigationBarTitleText": "%systemOnOff%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/MAC",
      "style": {
        "navigationBarTitleText": "%mac%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/MDC",
      "style": {
        "navigationBarTitleText": "%mdc%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/BMS",
      "style": {
        "navigationBarTitleText": "%bms%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/strategy",
      "style": {
        "navigationBarTitleText": "%strategy%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/schemeList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/jfpg/jfpgDetails",
      "style": {
        "navigationBarTitleText": "%jfpgDetails%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/operation/backup/backupDetails",
      "style": {
        "navigationBarTitleText": "%backupDetails%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/operation/price/priceDetails",
      "style": {
        "navigationBarTitleText": "%priceDetails%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/operation/jfpg/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/backup/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/price/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/instructLog/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/instructLog/instructDetails",
      "style": {
        "navigationBarTitleText": "%instructDetails%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/operation/jfpg/addJfpg",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/price/addPrice",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/backup/addBackup",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/question/index",
      "style": {
        "navigationBarTitleText": "%question%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/operation/question/quesDetails",
      "style": {
        "navigationBarTitleText": "%quesDetails%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/property/project/addProject",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/project/countryList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/device/addDevice",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/device/typeList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/sim/addSim",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/property/assignUser",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/common/selectTool",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/time/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/time/addTime",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/currency/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/operation/currency/addCurrency",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/monitor/vnc/setVnc",
      "style": {
        "navigationBarTitleText": "%remoteControl%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/vnc/vncInfo",
      "style": {
        "navigationBarTitleText": "%controlInfo%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/vnc/portList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/profile/notice/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/profile/notice/detail",
      "style": {
        "navigationBarTitleText": "%noticeDetail%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/param/upgrade",
      "style": {
        "navigationBarTitleText": "%upgrade%",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/monitor/upgrade/versionFileList",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/money",
      "style": {
        "navigationBarTitleText": "%incomeList%",
        "enablePullDownRefresh": false
      }
    }
  ],
  "tabBar": {
    "color": "#333",
    "selectedColor": "#229BFF",
    "borderStyle": "white",
    "backgroundColor": "#fff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "%tabbar.tabbar1%",
        "iconPath": "static/icon1.png",
        "selectedIconPath": "static/icon1-a.png"
      },
      {
        "pagePath": "pages/monitor/index",
        "text": "%tabbar.tabbar2%",
        "iconPath": "static/icon2.png",
        "selectedIconPath": "static/icon2-a.png"
      },
      {
        "pagePath": "pages/property/index",
        "text": "%tabbar.tabbar3%",
        "iconPath": "static/icon3.png",
        "selectedIconPath": "static/icon3-a.png"
      },
      {
        "pagePath": "pages/operation/index",
        "text": "%operation%",
        "iconPath": "static/icon4.png",
        "selectedIconPath": "static/icon4-a.png"
      },
      {
        "pagePath": "pages/profile/index",
        "text": "%tabbar.tabbar4%",
        "iconPath": "static/icon5.png",
        "selectedIconPath": "static/icon5-a.png"
      }
    ]
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#fff",
    "backgroundColor": "#F8F8F8",
    "pageOrientation": "auto"
  },
  "uniIdRouter": {}
}