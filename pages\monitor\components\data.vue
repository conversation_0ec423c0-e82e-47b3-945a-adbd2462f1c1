<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ isGroup ? monitorStore.groupControl[0]?.sdt: monitorStore.control?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="top-items flex justify-content">
    <view class="top-item flex u-flex-around" v-if="isShowDayOutputOfPlant">
      <view style="flex: 1">
        <view class="item-ti">{{ $t('电池装机容量') }}</view>
        <view><span class="item-num">{{ baseInfo.deviceBatteryCapacity }}</span><span class="item-unit">kWh</span>
        </view>
      </view>
      <image src="/static/deviceBatteryCapacity.png" mode="" class="item-img"></image>
    </view>
    <view class="top-item flex u-flex-around">
      <view style="flex: 1">
        <view class="item-ti">{{ $t('额定功率') }}</view>
        <view><span class="item-num">{{ baseInfo.deviceRatedPower }}</span><span class="item-unit">kW</span></view>
      </view>
      <image src="/static/deviceRatedPower.png" mode="" class="item-img"></image>
    </view>
    <view class="top-item flex u-flex-around" v-if="isShowPhotovoltaicInstalledCapacity">
      <view style="flex: 1">
        <view class="item-ti">{{ $t('光伏装机容量') }}</view>
        <view><span class="item-num">{{ baseInfo.photovoltaicInstalledCapacity }}</span><span
            class="item-unit">kWp</span></view>
      </view>
      <image src="/static/photovoltaicInstalledCapacity.png" mode="" class="item-img"></image>
    </view>
    <view class="top-item flex u-flex-around" v-if="isShowPhotovoltaicInstalledCapacity">
      <view style="flex: 1">
        <view class="item-ti">{{ $t('光伏发电量') }}</view>
        <view><span class="item-num">{{ baseInfo.dayPhotovoltaicPowerCapacityCalculate }}</span><span
            class="item-unit">kWh</span></view>
      </view>
      <image src="/static/dayPhotovoltaicPowerCapacityCalculate.png" mode="" class="item-img"></image>
    </view>
    <view class="top-item flex u-flex-around" v-if="isShowDayOutputOfPlant">
      <view style="flex: 1">
        <view class="item-ti">{{ dayOutputOfPlantCom.text }}</view>
        <view><span class="item-num">{{ baseInfo[dayOutputOfPlantCom.prop] }}</span><span class="item-unit">kWh</span></view>
      </view>
      <image src="/static/dayOutputOfPlant.png" mode="" class="item-img"></image>
    </view>
    <view class="top-item flex u-flex-around" v-if="isShowDayOutputOfPlant">
      <view style="flex: 1">
        <view class="item-ti">{{ dayElectricityConsumptionCom.text }}</view>
        <view><span class="item-num">{{ baseInfo[dayElectricityConsumptionCom.prop] }}</span><span class="item-unit">kWh</span>
        </view>
      </view>
      <image src="/static/dayElectricityConsumption.png" mode="" class="item-img"></image>
    </view>
  </view>
  <!-- 流动图 -->
  <view class="flow">
    <template v-if="!isGroup">
      <type_1 v-if="deviceType == 1 || deviceType == 10000"></type_1>
      <type_2 v-if="deviceType == 2"></type_2>
      <type_3 v-if="deviceType == 3"></type_3>
      <type_4 v-if="deviceType == 4 || deviceType == 10001"></type_4>
      <type_5 v-if="deviceType == 5"></type_5>
      <template v-if="deviceType == 6">
        <type_6 v-if="!isEmTypePCC"></type_6>
        <type_6_load v-if="isEmTypePCC"></type_6_load>
      </template>
      <template v-if="deviceType == 7">
        <type_9 v-if="isEmTypePCC"></type_9>
        <type_7 v-else></type_7>
      </template>
      <type_8 v-if="deviceType == 8"></type_8>
      <type_9 v-if="deviceType == 9"></type_9>
      <type_10 v-if="deviceType == 10"></type_10>
      <type_11 v-if="deviceType == 11"></type_11>
      <type_12 v-if="deviceType == 12"></type_12>
      <type_13 v-if="deviceType == 13"></type_13>
    </template>
    <template v-else>
      <template v-if="isDeviceMasterTypeRT07">
        <type_9 v-if="monitorStore.routeQuery.groupType[0] == 7"></type_9>
        <type_6_load v-if="monitorStore.routeQuery.groupType[0] == 6"></type_6_load>
      </template>
      <template v-else>
        <template v-if="deviceType == 10000">
          <type_1_pile v-if="isDeviceMasterTypeRT1112"></type_1_pile>
          <type_1 v-else></type_1>
        </template>
        <type_4 v-if="deviceType == 10001"></type_4>
        <template v-if="deviceType == 10002">
          <type_7 v-if="!isDeviceType1002Have910"></type_7>
          <type_9 v-else></type_9>
        </template>
      </template>
    </template>
  </view>
  <!-- 折线图 -->
  <view class="charts-box">
    <view class="flex justify-content align-items mb-20">
      <view class="flex align-items">
        <image src="../../../static/line.png" class="u-margin-6" style="width: 34rpx;height: 34rpx;"></image>
        <view>{{ $t('功率分析') }}</view>
      </view>
      <view>
        <image src="../../../static/landscape.png" class="u-margin-6" style="width: 34rpx;height: 34rpx;"
          @click="handleLandscapeMode('line')"></image>
      </view>
    </view>
    <!-- 功率分析日期筛选 -->
    <view class="flex align-items u-flex-center mb-20">
      <u-icon name="arrow-left" @click="handleCutLineClick('prev')"></u-icon>
      <view class="date-text" @click="openLineDate">
        {{ lineValue1 }}
      </view>
      <u-icon name="arrow-right" @click="handleCutLineClick('next')"></u-icon>
      <uv-calendars ref="calendar" mode="single" @confirm="confirm" :date="lineValue1" :color="otherColor.primaryColor"
        :confirmColor="otherColor.primaryColor"></uv-calendars>
    </view>
    <Line v-show="!isGroup"></Line>
    <LineGroup v-show="isGroup"></LineGroup>
  </view>
  <!-- 柱状图 -->
  <view class="charts-box">
    <view class="flex justify-content align-items mb-20">
      <view class="flex align-items">
        <image src="../../../static/bar.png" class="u-margin-6" style="width: 34rpx;height: 34rpx;"></image>
        <view>{{ $t('电量统计') }}</view>
      </view>
      <view>
        <image src="../../../static/landscape.png" class="u-margin-6" style="width: 34rpx;height: 34rpx;"
          @click="handleLandscapeMode('bar')"></image>
      </view>
      <!-- <view><u-button type="primary" :text="$t('导出报表')" size="small"></u-button></view> -->
    </view>
    <view class="flex justify-content mb-20">
      <u-subsection :list="dayList" mode="subsection" :current="current" fontSize="14px" style="width: 50%;"
        @change="sectionChange" :activeColor="otherColor.primaryColor"
        :inactiveColor="otherColor.mainColor"></u-subsection>
      <view class="flex align-items">
        <u-icon name="arrow-left" @click="handleCutClick('prev')"></u-icon>
        <view class="date-text" @click="handleYearMonthAndYear">
          {{ value1 }}
        </view>
        <u-icon name="arrow-right" @click="handleCutClick('next')"></u-icon>
      </view>
    </view>
    <Bar ref="barChart"></Bar>

    <u-datetime-picker :show="yearMonthShow" v-model="yearMonthValue" mode="year-month" format="YYYY-MM-DD"
      itemHeight="88" @confirm="handleYearMonthConfirm" @cancel="handleYearMonthCancel"></u-datetime-picker>
    <u-picker :show="yearShow" :columns="yearColumns" itemHeight="88" @confirm="handleYearConfirm"
      :closeOnClickOverlay="true" @close="handleYearCancel" @cancel="handleYearCancel" :cancelText="$t('取消')"
      :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    computed,
    ref,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad,
    onReady,
    onShow
  } from '@dcloudio/uni-app'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    dayjsFn
  } from '@/common/utils.js'
  import dayjs from 'dayjs'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn,
    isEnergyFn,
    isPhotovoltaicFn
  } from '@/hook/useDeviceType.js'
  import {
    emTypeOptions
  } from '@/constant/index.js'

  import Line from '../charts/line.vue'
  import LineGroup from '../charts/line-group.vue'
  import Bar from '../charts/bar.vue'

  import Type_1 from '../flow/type_1.vue'
  import Type_1_pile from '../flow/type_1_pile.vue'
  import Type_2 from '../flow/type_2.vue'
  import Type_3 from '../flow/type_3.vue'
  import Type_4 from '../flow/type_4.vue'
  import Type_5 from '../flow/type_5.vue'
  import Type_6 from '../flow/type_6.vue'
  import Type_6_load from '../flow/type_6_load.vue'
  import Type_7 from '../flow/type_7.vue'
  import Type_8 from '../flow/type_8.vue'
  import Type_9 from '../flow/type_9.vue'
  import Type_10 from '../flow/type_10.vue'
  import Type_11 from '../flow/type_11.vue'
  import Type_12 from '../flow/type_12.vue'
  import Type_13 from '../flow/type_13.vue'

  const {
    proxy
  } = getCurrentInstance()

  const emits = defineEmits(['orientationMode'])

  const monitorStore = useMonitorStore()
  const baseInfo = computed(() => monitorStore.baseInfo)

  const deviceType = computed(() => monitorStore.routeQuery.type)

  // 光伏装机容量
  const isShowPhotovoltaicInstalledCapacity = computed(() => {
    let {
      type
    } = monitorStore.routeQuery
    return isPhotovoltaicFn(type)
  })
  // 储能充放电量
  const isShowDayOutputOfPlant = (() => {
    let type = monitorStore.routeQuery.type
    return isEnergyFn(type)
  })
  // 是否为组合类型
  const isGroup = computed(() => {
    let type = monitorStore.routeQuery.type
    return isGroupFn(type)
  })

  const dayList = ref([{
      name: uni.$i18n().t('月')
    },
    {
      name: uni.$i18n().t('年')
    }
  ])
  const current = ref(0)
  const sectionChange = (index) => {
    if (index == 0) {
      value1.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
      yearMonthValue.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
    } else if (index == 1) {
      value1.value = uni.$u.timeFormat(new Date(), 'yyyy')
    }
    current.value = index
    changeDate()
  }


  const show = ref(false);
  const value1 = ref();
  value1.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
  const handleDateClick = () => {
    show.value = true
  }

  const handleYearMonthAndYear = () => {
    if (current.value == 0) {
      yearMonthShow.value = true
    } else {
      yearShow.value = true
    }
  }
  /**
   * 月
   */
  const yearMonthShow = ref(false)
  const yearMonthValue = ref()
  yearMonthValue.value = uni.$u.timeFormat(new Date(), 'yyyy-mm')
  const handleYearMonthConfirm = (e) => {
    value1.value = uni.$u.timeFormat(e.value, 'yyyy-mm')
    changeDate()
    yearMonthShow.value = false
  }
  const handleYearMonthCancel = () => {
    yearMonthShow.value = false
  }

  /**
   * 年
   */
  const yearShow = ref(false)
  const yearColumns = ref([
    ['2014', '2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025', '2026', '2027',
      '2028', '2029', '2030', '2031', '2032', '2033', '2034'
    ]
  ])
  const handleYearConfirm = (e) => {
    value1.value = e.value[0]
    changeDate()
    yearShow.value = false
  }
  const handleYearCancel = () => {
    yearShow.value = false
  }

  const changeDate = () => {
    let queryInfo = {
      type: undefined,
      endTime: '',
      startTime: '',
      deviceType: Number(monitorStore.routeQuery.type)
    }
    if (current.value == 0) {
      queryInfo.type = 2
      queryInfo.startTime = dayjsFn(value1.value).startOf('month').format('YYYY-MM-DD')
      queryInfo.endTime = dayjsFn(value1.value).endOf('month').format('YYYY-MM-DD')
    } else if (current.value == 1) {
      queryInfo.type = 3
      queryInfo.startTime = dayjsFn(value1.value).startOf('year').format('YYYY-MM-DD')
      queryInfo.endTime = dayjsFn(value1.value).endOf('year').format('YYYY-MM-DD')
    }
    monitorStore.queryInfo = queryInfo
    monitorStore.electricStatisticsFn(monitorStore.routeQuery.id)
  }
  // changeDate()

  /**
   * 日期切换
   */
  const handleCutClick = (type) => {
    let api = type == 'next' ? 'add' : 'subtract'
    if (current.value == 0) {
      let addDate = dayjsFn(value1.value)[api](1, 'month')
      if (type == 'next') {
        if (dayjsFn(addDate).isAfter(uni.$u.timeFormat(new Date(), 'yyyy-mm'))) return uni.showToast({
          icon: 'none',
          title: uni.$t('不可以选择今天以后的时间')
        })
      }
      value1.value = uni.$u.timeFormat(addDate, 'yyyy-mm')
    } else if (current.value == 1) {
      let addDate = dayjsFn(value1.value)[api](1, 'year')
      if (type == 'next') {
        if (dayjsFn(addDate).isAfter(uni.$u.timeFormat(new Date(), 'yyyy'))) return uni.showToast({
          icon: 'none',
          title: uni.$t('不可以选择今天以后的时间')
        })
      }
      value1.value = uni.$u.timeFormat(addDate, 'yyyy')
    }
    changeDate()
  }

  /**
   * 功率分析日期筛选
   */
  // console.log(monitorStore.control)
  const lineValue1 = ref()
  // lineValue1.value = monitorStore.control.sdt ? uni.$u.timeFormat(monitorStore.control.sdt, 'yyyy-mm-dd'): ''
  const openLineDate = () => {
    proxy.$refs.calendar.open()
  }
  const confirm = (e) => {
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt: monitorStore.control.sdt).utcOffset(utcOffset).format('YYYY-MM-DD')
    let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
      'YYYY-MM-DD')
    if (dayjsFn(e.fulldate).isAfter(nowDate)) return uni.showToast({
      icon: 'none',
      title: uni.$t('不可以选择今天以后的时间')
    })
    lineValue1.value = e.fulldate
    changeLineDate(e.fulldate)
  }
  const handleCutLineClick = (type) => {
    if (!(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt)) return uni.$u.toast('暂无数据')
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt: monitorStore.control.sdt).utcOffset(utcOffset).format('YYYY-MM-DD')
    let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
      'YYYY-MM-DD')
    let api = type == 'next' ? 'add' : 'subtract'
    let addDate = dayjsFn(lineValue1.value)[api](1, 'day')
    if (dayjsFn(addDate).isAfter(nowDate)) return uni.showToast({
      icon: 'none',
      title: uni.$t('不可以选择今天以后的时间')
    })
    lineValue1.value = uni.$u.timeFormat(addDate, 'yyyy-mm-dd')
    changeLineDate(lineValue1.value)
  }
  const changeLineDate = (date) => {
    let utcOffset = Number(decodeURIComponent(monitorStore.routeQuery.time).split(':')[0])
    let lineDate = ''
    if (date) {
      // let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt: monitorStore.control.sdt).utcOffset(utcOffset).format('YYYY-MM-DD')
      let nowDate = dayjs.utc(isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt).format(
        'YYYY-MM-DD')
      if (date == nowDate) lineDate = isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt
      if (dayjsFn(date).isBefore(nowDate)) lineDate = date + ' 23:59:59'
    } else {
      lineDate = isGroup.value ? monitorStore.groupControl[0].sdt : monitorStore.control.sdt
    }
    if (isGroup.value) {
      monitorStore.lineGroupQueryInfo = {
        ...monitorStore.lineGroupQueryInfo,
        date: lineDate
      }
      monitorStore.powerAnalysisStatisticsGroupFn({
        deviceSerialNumber: monitorStore.routeQuery.id,
        deviceType: monitorStore.routeQuery.type,
        timeZone: monitorStore.routeQuery.time,
        groupId: monitorStore.routeQuery.groupId,
        groupType: monitorStore.routeQuery.groupType,
      })

    } else {
      monitorStore.lineQueryInfo = {
        ...monitorStore.lineQueryInfo,
        date: lineDate
      }
      monitorStore.powerAnalysisStatisticsFn({
        deviceSerialNumber: monitorStore.routeQuery.id,
        deviceType: monitorStore.routeQuery.type,
        timeZone: monitorStore.routeQuery.time,
      })
    }
  }
  // changeLineDate()

  const initLineData = () => {
    monitorStore.deviceMonitoringDetailTopFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      deviceType: monitorStore.routeQuery.type
    })
    monitorStore.deviceMonitoringDetailRightFn({
      deviceSerialNumber: monitorStore.routeQuery.id,
      type: 'control',
      deviceType: monitorStore.routeQuery.type
    }).then((res) => {
      if (isGroup.value) {
        if (monitorStore.groupControl.length) {
          lineValue1.value = uni.$u.timeFormat(monitorStore.groupControl[0].sdt, 'yyyy-mm-dd')
          changeLineDate()
        }
      } else {
        if (monitorStore.control) {
          lineValue1.value = uni.$u.timeFormat(monitorStore.control.sdt, 'yyyy-mm-dd')
          changeLineDate()
        }
      }
    })
  }
  onShow(() => {
    initLineData()
    changeDate()
  })

  // 图表横屏模式
  const handleLandscapeMode = (type) => {
    monitorStore.orientation.landscape = true
    monitorStore.orientation.isLine = type == 'bar' ? false : true
    uni.navigateTo({
      url: '/pages/monitor/charts/landscape'
    })
  }

  // 组合设备类型1002是否含有9、10类型
  const isDeviceType1002Have910 = computed(() => {
    let groupType = monitorStore.routeQuery.groupType
    if (monitorStore.routeQuery.type == 10002)
      if (groupType.findIndex(item => item == 9 || item == 10) !== -1) return true
    return false
  })

  // 储能系统
  const isEmTypePCC = computed(() => {
    let data = monitorStore.pcs_ele
    return (monitorStore.routeQuery.type == 7 || monitorStore.routeQuery.type == 6) && data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
  })
  // 组合设备类型，主机为RT07储能系统(纯并网)、PCC电表，电池功率-电表有功功率,取绝对值
  const isDeviceMasterTypeRT07 = computed(() => {
    let groupType = monitorStore.routeQuery.groupType
    let data = monitorStore.pcs_ele
    let isEmTypePCC = data.some(item => {
      return emTypeOptions.findIndex(range => item.dc >= range.min && item.dc <= range.max) !== -1
    })
    if ((groupType[0] == 7 || groupType[0] == 6) && isEmTypePCC) return true
    return false
  })
  // 组合设备类型，主机为光储充类型时，负载改为充电桩功率
      const isDeviceMasterTypeRT1112 = computed(() => {
        let groupType = monitorStore.routeQuery.groupType
        let data = groupType[0] == 11 || groupType[0] == 12
        return data
      })
  
  /**
   * 正向电量
   */
  const dayOutputOfPlantCom = computed(() => monitorStore.routeQuery.type == 13 ? {
    text: uni.$t('正向电量'),
    prop: 'jk_1083'
  }: {
    text: uni.$t('储能充电量'),
    prop: 'dayOutputOfPlant'
  } )
  const dayElectricityConsumptionCom = computed(() => monitorStore.routeQuery.type == 13 ? {
    text: uni.$t('反向电量'),
    prop: 'jk_1084'
  }: {
    text: uni.$t('储能放电量'),
    prop: 'dayElectricityConsumption'
  } )


  defineExpose({
    changeLineDate,
    lineValue1
  })
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }

  .top-items {
    background-color: $uni-bg-color;
    border-radius: $uni-border-radius-lg;
    padding: 30rpx;
    margin: 20rpx 30rpx;
    display: grid;
    grid-template-columns: repeat(2, 48%);
    grid-gap: 30rpx;
    justify-content: center;

    .top-item {
      // background-color: $uni-bg-color-grey;
      width: 100%;
      border-radius: $uni-border-radius-lg;
      align-items: center;

      .item-img {
        width: 80rpx;
        height: 80rpx;
      }

      .item-ti {
        color: $uni-text-color-grey;
        margin-bottom: 20rpx;
        font-size: 12px;
      }

      .item-num {
        font-size: 16px;
        font-weight: bold;
        margin-right: 8rpx;
      }

      .item-unit {
        font-size: 10px;
        color: $uni-text-color-grey;
      }
    }

    .top-item:nth-child(2n) {
      margin-right: 0;
    }
  }

  .flow {
    width: calc(100% - 60rpx);
    background-color: $uni-bg-color;
    margin: 0 30rpx;
    margin-bottom: 20rpx;
    border-radius: $uni-border-radius-lg;
  }

  .charts-box {
    width: calc(100% - 60rpx);
    // height: 600rpx;
    background-color: $uni-bg-color;
    margin: 0 30rpx;
    padding: 20rpx;
    border-radius: $uni-border-radius-lg;
    margin-bottom: 20rpx;
  }

  .date-text {
    margin: 0 20px;
    color: $uni-text-color-grey;
  }

  :deep(.uni-date-x--border) {
    border: none !important;
  }

  :deep(.uni-date-x .icon-calendar) {
    display: none;
  }

  :deep(.uni-date__x-input) {
    text-align: center;
    padding: 0;
    margin: 0 20rpx;
  }
</style>