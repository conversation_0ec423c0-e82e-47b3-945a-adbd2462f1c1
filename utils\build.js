/*
 * 脚本执行顺序:
 * 1. 通过inquirer,获取用户的选择
 * 2. 更改配置文件内容：包括：manifest配置，build.json打包配置文件，隐私文件配置文件等。
 * 3. 执行命令行命令：cli.exe pack --config build.json
 */
const inquirer = require('inquirer')
const fs = require('fs-extra')
const path = require('path')
const {
  exec
} = require('child_process')
const iconv = require('iconv-lite')

const APPID = {
  '亿兰科云平台': {
    appId: '__UNI__655B477',
    bundle: 'uni.UNI655B477'
  }
}

const resourcePath = 'C:/Users/<USER>/Desktop/定制app'

// 命令行交互，返回值是promise對象
inquirer.prompt([{
  type: 'list',
  message: '请选择打包应用',
  name: 'appName',
  choices: ['亿兰科云平台']
},
{
  type: 'list',
  message: '请选择平台',
  name: 'platform',
  choices: ['android', 'ios', 'android,ios']
},
{
  type: 'list',
  message: '请选择是否自定义基座',
  name: 'iscustom',
  choices: ['是', '否']
}
]).then(res => {
  console.log("\n您选择的是：", res)
  // 获取配置文件中的应用名，应用ID，以及manifest的所有数据，readManifest方法定义在下方
  let {
    appName,
    data,
    appId
  } = readManifest()
  // 如果选择的appname与目前配置文件的appname不同
  if (appName === res.appName) {
    console.log('\n应用名不变，无需修改配置文件')
    return
  }
  // 修改app相关配置文件，replaceManifest方法定义在下方
  replaceManifest(appName, res.appName, appId, data)
  // 修改隐私政策文件，replacePrivacy方法定义在下方
  replacePrivacyAndEnv(appName, res.appName)
  // 修改图片及主题
  replaceImgAndTheme(res.appName)
  // 修改打包配置文件build.json，replaceBuild方法定义在下方
  replaceBuild(res.appName, res.platform, res.iscustom)
  console.log('\n开始打包')
  // 获取打包配置的路径
  const buildPath = path.resolve(__dirname, '../buildConfig.json')
  // 执行打包命令，commandSpawn方法定义在下方
  commandSpawn(`cli.exe pack --config ${buildPath}`)
})

/**
 * 执行命令行命令
 * @param {Object} arg cli命令打包app
 */
const commandSpawn = function (arg) {
  return new Promise((resolve, reject) => {
    // exec执行后会返回一个子进程，encoding: 'binary'配合iconv解决乱码问题
    const childProcess = exec(arg, {
      encoding: 'binary'
    })
    // 将子进程的输出流放在主进程中
    childProcess.stdout.on('data', data => {
      console.log(iconv.decode(Buffer.from(data, 'binary'), 'gbk'))
    })
    // 将子进程的错误信息放在主进程中
    childProcess.stderr.on('data', data => {
      console.log(iconv.decode(Buffer.from(data, 'binary'), 'gbk'))
    })
    // 监听子进程结束
    childProcess.on('exit', () => {
      resolve()
    })
  })
}

/**
 * 修改build.json文件
 * @param {Object} appname 新应用名称
 * @param {Object} platform 要打包的平台
 * @param {Object} iscustom 是否自定义基座
 */
const replaceBuild = function (appname, platform, iscustom) {
  // 读取build.json文件的数据
  const appNameData = JSON.parse(fs.readFileSync(`/Users/<USER>/Desktop/定制app/${appname}/buildConfig.json`, 'utf-8'))
  // 设置打包平台
  appNameData.platform = platform
  // ios证书分发布证书和dev证书
  if (iscustom == '是') {
    appNameData.iscustom = true
    if (platform.indexOf('ios') !== -1) setIosBuildConfig(appNameData, appname, true)
    if (platform.indexOf('android') !== -1) setAndroidBuildConfig(appNameData, appname)
  } else {
    appNameData.iscustom = false
    if (platform.indexOf('ios') !== -1) setIosBuildConfig(appNameData, appname, false)
    if (platform.indexOf('android') !== -1) setAndroidBuildConfig(appNameData, appname)
  }
  // 修改打包配置
  fs.writeFileSync('buildConfig.json', JSON.stringify(appNameData))
  console.log('修改build.json文件成功')
}

const setAndroidBuildConfig = (appNameData, appname) => {
  appNameData.android.packagename = APPID[appname].bundle
  let files = fs.readdirSync(`${resourcePath}/${appname}/secretKey/android`, 'utf-8')
  let keystoreFileName = files.find(item => item.indexOf('.keystore') !== -1)
  appNameData.android.certfile = `${resourcePath}/${appname}/secretKey/android/${keystoreFileName}`
  appNameData.android.certalias = keystoreFileName.replace('.keystore', '')
  let pwdFileName = files.find(item => item.indexOf('.txt') !== -1)
  if (pwdFileName) appNameData.android.certpassword = pwdFileName.replace('.txt', '')
}
const setIosBuildConfig = (appNameData, appname, iscustom) => {
  appNameData.ios.bundle = APPID[appname].bundle
  // let files = fs.readdirSync(`${resourcePath}/${appname}/secretKey/ios${iscustom ? '' : '/release'}`, 'utf-8')
  // appNameData.ios.profile = `${resourcePath}/${appname}/secretKey/ios/${iscustom ? '' : 'release/'}${files.find(item => item.indexOf('.mobileprovision') !== -1)}`
  // appNameData.ios.certfile = `${resourcePath}/${appname}/secretKey/ios/${iscustom ? '' : 'release/'}${files.find(item => item.indexOf('.p12') !== -1)}`
  let files = fs.readdirSync(`${resourcePath}/${appname}/secretKey/ios`, 'utf-8')
  appNameData.ios.profile = `${resourcePath}/${appname}/secretKey/ios/${files.find(item => item.indexOf('.mobileprovision') !== -1)}`
  appNameData.ios.certfile = `${resourcePath}/${appname}/secretKey/ios/${files.find(item => item.indexOf('.p12') !== -1)}`
  let pwdFileName = files.find(item => item.indexOf('.txt') !== -1)
  if (pwdFileName) appNameData.ios.certpassword = pwdFileName.replace('.txt', '')
}

/**
 * 读取manifest文件
 */
const readManifest = function () {
  const data = JSON.parse(fs.readFileSync('manifest.json', 'utf-8'))
  const appName = data.name
  const appId = data.appid
  return {
    appName,
    appId,
    data
  }
}

/**
 * 修改manifest文件
 * @param {Object} oldName 旧应用名称
 * @param {Object} newName 新应用名称
 * @param {Object} oldId 旧appid
 * @param {Object} data 旧manifest文件
 */
const replaceManifest = function (oldName, newName, oldId, data) {
  // 修改名字
  // data.name = newName
  // 修改id
  data.appid = APPID[newName].appId
  // 修改应用图标
  data['app-plus'].distribute.icons = {
    "android": {
      "hdpi": `${resourcePath}/${newName}/icons/72x72.png`,
      "xhdpi": `${resourcePath}/${newName}/icons/96x96.png`,
      "xxhdpi": `${resourcePath}/${newName}/icons/144x144.png`,
      "xxxhdpi": `${resourcePath}/${newName}/icons/192x192.png`
    },
    "ios": {
      "appstore": `${resourcePath}/${newName}/icons/1024x1024.png`,
      "ipad": {
        "app": `${resourcePath}/${newName}/icons/76x76.png`,
        "app@2x": `${resourcePath}/${newName}/icons/152x152.png`,
        "notification": `${resourcePath}/${newName}/icons/20x20.png`,
        "notification@2x": `${resourcePath}/${newName}/icons/40x40.png`,
        "proapp@2x": `${resourcePath}/${newName}/icons/167x167.png`,
        "settings": `${resourcePath}/${newName}/icons/29x29.png`,
        "settings@2x": `${resourcePath}/${newName}/icons/58x58.png`,
        "spotlight": `${resourcePath}/${newName}/icons/40x40.png`,
        "spotlight@2x": `${resourcePath}/${newName}/icons/80x80.png`
      },
      "iphone": {
        "app@2x": `${resourcePath}/${newName}/icons/120x120.png`,
        "app@3x": `${resourcePath}/${newName}/icons/180x180.png`,
        "notification@2x": `${resourcePath}/${newName}/icons/40x40.png`,
        "notification@3x": `${resourcePath}/${newName}/icons/60x60.png`,
        "settings@2x": `${resourcePath}/${newName}/icons/58x58.png`,
        "settings@3x": `${resourcePath}/${newName}/icons/87x87.png`,
        "spotlight@2x": `${resourcePath}/${newName}/icons/80x80.png`,
        "spotlight@3x": `${resourcePath}/${newName}/icons/120x120.png`
      }
    }
  }
  // 修改完成写入manifest文件
  fs.writeFileSync('manifest.json', JSON.stringify(data))
  console.log('\n修改manifest.json文件(应用名称、appid、应用图标)成功')
}

/**
 * 修改隐私政策配置
 * @param {Object} oldName 旧应用名称
 * @param {Object} newName 新应用名称
 */
const replacePrivacyAndEnv = function (oldName, newName) {
  // 读取文件内容
  // 安卓隐私弹框
  let androidPrivacyData = fs.readFileSync(`${resourcePath}/${newName}/agreement/androidPrivacy.json`, 'utf-8')
  // 内置隐私.vue\.html文件
  let privacyData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy-policy.vue`, 'utf-8')
  let privacyEnData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy-policy-en.vue`, 'utf-8')
  let privacyItData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy-policy-it.vue`, 'utf-8')
  let serviceData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service-terms.vue`, 'utf-8')
  let serviceEnData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service-terms-en.vue`, 'utf-8')
  let serviceItData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service-terms-it.vue`, 'utf-8')

  let privacyHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy.html`, 'utf-8')
  let privacyEnHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy-en.html`, 'utf-8')
  let privacyItHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/privacy-it.html`, 'utf-8')
  let serviceHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service.html`, 'utf-8')
  let serviceEnHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service-en.html`, 'utf-8')
  let serviceItHtmlData = fs.readFileSync(`${resourcePath}/${newName}/agreement/service-it.html`, 'utf-8')
  // 服务
  let devData = fs.readFileSync(`${resourcePath}/${newName}/env/.env.development`, 'utf-8')
  let prodData = fs.readFileSync(`${resourcePath}/${newName}/env/.env.production`, 'utf-8')
  // 修改应用名称相关、国际化
  let langData = require(`${resourcePath}/${newName}/lang.js`)
  let zhData = require(path.resolve(__dirname, '../locale/zh-Hans.json'))
  let enData = require(path.resolve(__dirname, '../locale/en.json'))
  let itData = require(path.resolve(__dirname, '../locale/it.json'))
  zhData['app.name'] = langData.zh['app.name']
  zhData['login.welcome'] = langData.zh['login.welcome']
  zhData['aboutApp.name'] = langData.zh['aboutApp.name']
  zhData['aboutApp.filing'] = langData.zh['aboutApp.filing']
  enData['app.name'] = langData.en['app.name']
  enData['login.welcome'] = langData.en['login.welcome']
  enData['aboutApp.name'] = langData.en['aboutApp.name']
  enData['aboutApp.filing'] = langData.en['aboutApp.filing']
  itData['app.name'] = langData.it['app.name']
  itData['login.welcome'] = langData.it['login.welcome']
  itData['aboutApp.name'] = langData.it['aboutApp.name']
  itData['aboutApp.filing'] = langData.it['aboutApp.filing']
  // 写入文件
  fs.writeFileSync('.env.development', devData)
  fs.writeFileSync('.env.production', prodData)
  fs.writeFileSync('pages/common/privacy-policy.vue', privacyData)
  fs.writeFileSync('pages/common/privacy-policy-en.vue', privacyEnData)
  fs.writeFileSync('pages/common/privacy-policy-it.vue', privacyItData)
  fs.writeFileSync('pages/common/service-terms.vue', serviceData)
  fs.writeFileSync('pages/common/service-terms-en.vue', serviceEnData)
  fs.writeFileSync('pages/common/service-terms-it.vue', serviceItData)
  fs.writeFileSync('privacy.html', privacyHtmlData)
  fs.writeFileSync('privacy-en.html', privacyEnHtmlData)
  fs.writeFileSync('privacy-it.html', privacyItHtmlData)
  fs.writeFileSync('service.html', serviceHtmlData)
  fs.writeFileSync('service-en.html', serviceEnHtmlData)
  fs.writeFileSync('service-it.html', serviceItHtmlData)
  fs.writeFileSync('androidPrivacy.json', androidPrivacyData)
  fs.writeFileSync('locale/zh-Hans.json', JSON.stringify(zhData))
  fs.writeFileSync('locale/en.json', JSON.stringify(enData))
  fs.writeFileSync('locale/it.json', JSON.stringify(itData))
  console.log('修改androidPrivacy.json、env文件成功')
}

/**
 * 修改图片与主题
 */
const replaceImgAndTheme = (appName) => {
  // 清空源文件夹内容(img)
  fs.emptyDirSync('static')
  // 复制文件
  mapFiles('C:\\Users\\<USER>\\Desktop\\定制app\\' + appName + '\\img', path.resolve(__dirname, '../static'))
  // 主题
  fs.removeSync('uni.scss')
  fs.removeSync('common/theme.scss')
  fs.removeSync('common/other.module.scss')
  fs.removeSync('uni_modules/uview-plus/theme.scss')
  fs.copyFileSync(`${resourcePath}/${appName}/css/uni.scss`, 'uni.scss')
  fs.copyFileSync(`${resourcePath}/${appName}/css/theme.scss`, 'common/theme.scss')
  fs.copyFileSync(`${resourcePath}/${appName}/css/other.module.scss`, 'common/other.module.scss')
  fs.copyFileSync(`${resourcePath}/${appName}/css/uTheme.scss`, 'uni_modules/uview-plus/theme.scss')
  console.log('修改图片与主题文件成功')
}

const mapFiles = (src, dest) => {
  // 判断是否是文件夹
  const isDir = fs.statSync(src).isDirectory()
  if (isDir) {
    try {
      // 判断所要创建的项目是否已经存在
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest);
      }
      fs.readdir(src, null, (err, files) => {
        if (err) {
          return console.log(err)
        }
        files.forEach(file => {
          mapFiles(path.join(src, file), path.join(dest, file))
        })
      })
    } catch (err) {
      console.error(err);
    }
  } else {
    // 不是文件夹就复制到新的文件夹里
    fs.copyFileSync(src, dest)
  }
}