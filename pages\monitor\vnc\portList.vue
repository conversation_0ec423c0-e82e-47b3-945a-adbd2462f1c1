<template>
  <view class="scheme-list">
    <u-navbar :title="navBarTitle" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;"
          @click="handleCconfirmClick">{{ $t('完成') }}</u-button>
      </template>
    </u-navbar>

    <view class="scheme-list-wrap">
      <view class="scheme-list-wrap-item flex" v-for="item in portOptions" :key="item.id"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.id, 'color-grey': item.disabled }">
          {{ item.port }}
        </view>
        <view v-if="item.id == initData.id">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view v-if="!portOptions.length"
      style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    listAll
  } from '@/api/monitor'

  const initData = ref()
  const navBarTitle = ref()
  onLoad((options) => {
    initData.value = {
      ...options
    }
    if (initData.value.type == 'vnc') {
      navBarTitle.value = uni.$t('选择VNC端口')
    } else if (initData.value.type == 'ssh') {
      navBarTitle.value = uni.$t('选择SSH端口')
    }
  })
  onShow(() => {
    getPortFn()
  })
  const selectData = ref()
  const handleItemClick = (item) => {
    if (item.disabled) return
    selectData.value = item
    initData.value = item
  }
  const handleCconfirmClick = () => {
    setTimeout(() => {
      uni.$emit(
        'portItemClick', selectData.value)
      uni.navigateBack({
        delta: 1
      })
    }, 300)
  }

  // 获取端口
  const portOptions = ref([])
  const getPortFn = () => {
    listAll().then(res => {
      if (res.code !== 200) return uni.$u.toast(res.msg)
      let data = res.data.map(item => {
        return {
          id: item.id,
          port: item.port,
          disabled: item.status == 0,
          type: item.type
        }
      })
      portOptions.value = data.filter(item => item.type == (initData.value.type == 'vnc' ? 1 : 0))
    })
  }
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item1 {
        padding: 10rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>