<template>
  <view class="add-box">
    <u-navbar leftText="" :title="title" bgColor="#fff" leftIconSize="20px" :autoBack="true" :placeholder="true">
    </u-navbar>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item u-p-b-10">
        <u-form-item :label="$t('项目名称')" prop="projectName" required :borderBottom="true">
          <u-input v-model="form.projectName" :placeholder="$t('请输入项目名称')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('国家')" prop="country" required :borderBottom="true"
          @click="handleCountryCell('country')">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ countryText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('区域')" prop="projectArea" required :borderBottom="true"
          @click="handleCountryCell('area')">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ addressText }}
            <!-- <uni-data-picker :placeholder="$t('请选择')" :popup-title="$t('请选择所在地区')" :localdata="dataTree" v-model="area"
              @change="onchange" @nodeclick="onnodeclick" ref="areaRef">
            </uni-data-picker> -->
          </view>
        </u-form-item>
        <u-form-item :label="$t('项目地址')" prop="projectAddress" required :borderBottom="true">
          <u-input v-model="form.projectAddress" :placeholder="$t('请输入项目地址')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('经度')" prop="projectLatitudex" required :borderBottom="true">
          <u-input v-model="form.projectLatitudex" :placeholder="$t('请输入经度')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('纬度')" prop="projectLatitudey" required :borderBottom="true">
          <u-input v-model="form.projectLatitudey" :placeholder="$t('请输入纬度')" border="none"></u-input>
        </u-form-item>
        <u-form-item :label="$t('时区')" prop="timeZoneId" required :borderBottom="true"
          @click="handleCountryCell('time')">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ timeZoneText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('货币')" prop="countryCurrencyId" required :borderBottom="true"
          @click="handleCountryCell('currency')">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ currencyText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('设备')" prop="deviceIds">
        </u-form-item>
        <view class="u-m-t-20 flex u-flex-wrap u-flex-between
        ">
          <view class="device-item" v-for="(item, index) in form.deviceIds" :key="item.deviceId"
            @click="handleDeviceItemClick(item)">
            <view class="item-ti u-line-1">{{ item.deviceName }}</view>
            <view class=" u-line-1">{{ $t('额定功率') }}：<span class="ri-num">{{ item.deviceRatedPower }}</span>kW</view>
            <view class=" u-line-1">{{ $t('电池容量') }}：<span
                class="ri-num">{{ item.deviceBatteryCapacity || '--' }}</span>kWh</view>
            <image src="../../../static/removeDevice.webp" mode="" class="device-item-de" @click.stop="handleRemoveDeviceClick(index)" />
          </view>
          <view class="device-item device-add" @click="handleCountryCell('device')">
            <u-icon name="plus" size="40"></u-icon>
          </view>
        </view>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40"
      @click="handleConfirmClick">{{ $t('确认') }}</u-button>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" :keyName="lang == 'zh-Hans' ? 'text': 'id'"
      :columns="selectCom" itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true"
      @close="handleSelectCancel" @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"
      @change="changeHandler" ref="pickerRef"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    nextTick,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    addProject,
    editProject,
    getProjectInfo
  } from '@/api/project'
  import {
    areaList
  } from './world'
  import {
    timeList
  } from '@/api/time'
  import {
    allCurrency
  } from '@/api/currency'

  const {
    proxy
  } = getCurrentInstance()
  const title = ref(uni.$t('添加项目'))
  const id = ref()
  onLoad((options) => {
    if (options.id) {
      id.value = options.id
      title.value = uni.$t('修改项目')
    } else {
      title.value = uni.$t('添加项目')
    }
    getCurrentcyList()
    if (service !== 'zh') rules.value.projectName.push({
      message: uni.$t('不允许有中文字符'),
      pattern: /^[^\u4e00-\u9fa5]+$/,
      trigger: ['blur', 'change'],
    })
  })

  const getInfoFn = async () => {
    const res = await getProjectInfo({
      projectId: id.value
    })
    area.value = res.data.projectArea.split(',')
    countryText.value = res.data.country
    timeZoneText.value = res.data[getPropFn()]
    currencyText.value = currencyData.value.find(item => item.id == res.data.countryCurrencyId)?.currency
    addressText.value = res.data.projectArea
    form.value = {
      ...res.data,
      deviceIds: res.data.ylkDevices
    }
    setAreaData()
  }
  const setAreaData = () => {
    if (!form.value.projectArea) return
    let area1 = form.value.projectArea.split(',')
    let province = areaList.find(item => item.id == form.value.country)?.children
    let prop = lang == 'zh-Hans' ? 'text': 'id'
    let provinceIndex = province.findIndex(item => item[prop] == area1[0])
    let city = province[provinceIndex]?.children
    let cityIndex = city?.findIndex(item => item[prop]== area1[1])
    if (area1.length == 3) {
      let district = city[cityIndex]?.children
      let districtIndex = district.findIndex(item => item[prop] == area1[2])
      defaultSelectIndex.value = [provinceIndex, cityIndex, districtIndex]
      selectCom.value = [
        province,
        city,
        district
      ]
    } else if (area1.length == 2) {
      defaultSelectIndex.value = [provinceIndex, cityIndex]
      selectCom.value = [
        province,
        city
      ]
    }
  }

  const formRef = ref(null)
  const form = ref({
    projectName: '',
    country: '中国',
    projectArea: '',
    projectAddress: '',
    projectLatitudex: '',
    projectLatitudey: '',
    timeZoneId: '',
    countryCurrencyId: '',
    deviceIds: [],
  })
  const rules = ref({
    'projectName': [
      {
        type: 'string',
        required: true,
        message: uni.$t('请输入项目名称'),
        trigger: ['blur', 'change'],
      }
    ],
    'country': {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'projectArea': {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'countryCurrencyId': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'timeZoneId': {
      type: 'number',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
    'projectAddress': {
      type: 'string',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'projectLatitudex': {
      type: 'number',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
    'projectLatitudey': {
      type: 'number',
      required: true,
      message: uni.$t('请输入'),
      trigger: ['blur', 'change'],
    },
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          if (title.value == uni.$t('添加项目')) {
            addProject({
              ...form.value,
              deviceIds: form.value.deviceIds.map(item => item.deviceId)
            }).then(res => {
              uni.$u.toast(uni.$t('添加成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('添加失败'))
            })
          } else {
            editProject({
              ...form.value,
              deviceIds: form.value.deviceIds ? form.value.deviceIds.map(item => item.deviceId): [],
            }).then(res => {
              uni.$u.toast(uni.$t('修改成功'))
              uni.navigateBack()
            }).catch(() => {
              uni.$u.toast(uni.$t('修改失败'))
            })
          }
        }
      })
    }
  }

  /**
   * 国家
   */
  const selectType = ref()
  const handleCountryCell = (type) => {
    selectType.value = type
    if (type == 'country') {
      uni.navigateTo({
        url: `/pages/property/project/countryList?value=${form.value.country}&type=${type}`
      })
    } else if (type == 'time') {
      uni.navigateTo({
        url: `/pages/property/project/countryList?value=${form.value.timeZoneId}&type=${type}`
      })
    } else if (type == 'currency') {
      uni.navigateTo({
        url: `/pages/property/project/countryList?value=${form.value.countryCurrencyId}&type=${type}`
      })
    } else if (type == 'device') {
      uni.navigateTo({
        url: `/pages/property/project/countryList?type=${type}&value=${form.value.deviceIds?.reduce((pre, cur) => {
      return `${pre}${pre ? ',': ''}${cur.deviceId}`
    }, '')}`
      })
      setTimeout(() => {
        uni.$emit('addBindDeviceClick', form.value.deviceIds)
      }, 300)
    } else if (type == 'area') {
      if (!dataTree.value) {
        return uni.$u.toast(uni.$t('该国家没有数据'))
      }
      setAreaData()
      isShowSelect.value = true
    }
  }
  const countryText = ref('中国')
  const addressText = ref('')
  const timeZoneText = ref('')
  const currencyText = ref('')
  const lang = uni.cache.getItem('language')
  uni.$on('countryItemClick', (data) => {
    if (!data) return
    if (selectType.value == 'country') {
      form.value.country = data.value
      countryText.value = lang == 'zh-Hans' ? data.text : data.value
      if (!dataTree.value) {
        defaultSelectIndex.value = [0]
        selectCom.value = []
        form.value.projectArea = ''
        addressText.value = ''
        return
      }
      let city = dataTree.value[0]?.children
      if (city[0]?.children) {
        form.value.projectArea = lang == 'zh-Hans' ?
          `${dataTree.value[0].text},${city[0].text},${city[0]?.children[0].text}` :
          `${dataTree.value[0].id},${city[0].id},${city[0]?.children[0].id}`
        addressText.value = form.value.projectArea
      } else {
        form.value.projectArea = lang == 'zh-Hans' ? `${dataTree.value[0].text},${city[0].text}` :
          `${dataTree.value[0].id},${city[0].id}`
        addressText.value = form.value.projectArea
      }
    } else if (selectType.value == 'time') {
      form.value.timeZoneId = data.id
      timeZoneText.value = data[getPropFn()]
    } else if (selectType.value == 'currency') {
      form.value.countryCurrencyId = data.id
      currencyText.value = `${data.country} - ${data.currency}`
    } else if (selectType.value == 'device') {
      form.value.deviceIds = [...data]
    }
  })
  const service = uni.cache.getItem('service')
  onUnload(() => {
    uni.$off('countryItemClick')
  })
  const area = ref([])
  const onchange = (e) => {
    form.value.projectArea = e.detail.value.reduce((pre, cur) => {
      return `${pre}${pre ? ',': ''}${cur.value}`
    }, '')
  }
  const onnodeclick = (e) => {
    if (e?.parent_value && e.value == e?.parent_value) {
      area.value = [e]
      form.value.projectArea = `${e.value},${e.value}`
      nextTick(() => {
        proxy.$refs.areaRef.hide()
      })
    }
  }
  const dataTree = computed(() => {
    return areaList.find(item => item.id == form.value.country)?.children
  })

  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0, 0, 0])
  const selectCom = ref([])
  selectCom.value = [
    dataTree.value,
    dataTree.value[0]?.children,
    dataTree.value[0]?.children[0]?.children,
  ]
  const handleSelectConfirm = ({
    value
  }) => {
    form.value.projectArea = value.reduce((pre, cur) => {
      return `${pre}${pre ? ',': ''}${cur[lang == 'zh-Hans' ? 'text': 'id']}`
    }, '')
    addressText.value = form.value.projectArea
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    isShowSelect.value = false
  }
  const changeHandler = ({
    columnIndex,
    value,
    values,
    index,
  }) => {
    if (columnIndex === 0) {
      proxy.$refs.pickerRef.setColumnValues(1, dataTree.value[index]?.children);
      if (dataTree.value[index]?.children[0]?.children) proxy.$refs.pickerRef.setColumnValues(2, dataTree.value[index]
        ?.children[0]?.children);
    } else if (columnIndex === 1) {
      if (value[1]?.children) proxy.$refs.pickerRef.setColumnValues(2, value[1]?.children);
    }
  }

  /**
   * 选择设备
   */
  const handleDeviceItemClick = (item) => {
    uni.navigateTo({
      url: `/pages/property/device/device-detail?id=${item.deviceId}&isShow=false`
    })
  }
  const handleRemoveDeviceClick = (index) => {
    form.value.deviceIds.splice(index, 1)
    uni.$u.toast(uni.$t('删除成功'))
  }
  /**
   * 获取货币
   */
  const currencyData = ref([])
  const getCurrentcyList = async () => {
    const res = await allCurrency()
    currencyData.value = res.data
    if (title.value == uni.$t('修改项目')) getInfoFn()
  }
  const getPropFn = () => {
    const lang = uni.cache.getItem('language')
    switch (lang) {
      case 'zh':
        return 'timeZoneAddress'
      case 'en':
        return 'timeZoneAddressUs'
      case 'it':
        return 'timeZoneAddressIt'
      default:
        return 'timeZoneAddress'
    }
  }
</script>

<style lang="scss" scoped>
  .add-box {
    width: 100%;
    height: 100vh;
    padding: 20rpx 30rpx;
    overflow: auto;
  }

  .add-item {
    background-color: #fff;
    padding: 0 30rpx;
    border-radius: 6px;
  }

  .btn {
    height: 50rpx;
    background: rgba(0, 0, 0, 0);
  }

  .device-item {
    background-color: $uni-bg-color-grey;
    padding: 16rpx;
    border-radius: 20rpx;
    font-size: 12px;
    margin-bottom: 20rpx;
    line-height: 40rpx;
    width: 48%;
    position: relative;

    .item-ti {
      font-size: 14px;
      font-weight: bold;
    }

    &-de {
      width: 30rpx;
      height: 30rpx;
      margin-right: 20rpx;
      position: absolute;
      right: -30rpx;
      top: -10rpx;
    }
  }

  .device-add {
    height: 152rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-button__text) {
    font-size: 12px !important;
  }

  :deep(.u-datetime-picker) {
    .u-input {
      padding: 0 !important;
      border: none;
    }
  }

  :deep(.rate) {
    .u-form-item__body__right__content__slot {
      flex-direction: row-reverse !important;
    }
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }

  :deep(.uni-data-tree-input) {
    .input-value-border {
      border: none !important;
    }

    .selected-area {
      align-items: flex-end;
      justify-content: flex-end;
    }

    .selected-list {
      justify-content: flex-end;
    }
  }
</style>