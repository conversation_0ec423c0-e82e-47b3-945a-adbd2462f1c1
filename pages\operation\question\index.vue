<template>
  <view class="ques-box">
    <view class="ques-box-view">
      <view class="view-item flex justify-content align-items" v-for="item in contData" :key="item.value" @click="handleCellClick(item)">
        <view>{{ item.text }}
        </view>
        <view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref } from 'vue'
  
  const contData = ref([
    {
      text: uni.$t('页面数据加载速度过慢？'),
      value: 1
    },
    {
      text: uni.$t('选择服务器说明？'),
      value: 2
    },
    {
      text: uni.$t('APP更新后页面卡住？'),
      value: 3
    }
  ])
  const handleCellClick = (item) => {
    uni.navigateTo({
      url: `/pages/operation/question/quesDetails?value=${item.value}`
    })
  }
</script>

<style lang="scss" scoped>
  .ques-box {
    height: 100vh;
    width: 100%;
    padding: 20rpx 30rpx;

    &-view {
      background-color: #fff;
      padding: 0 30rpx;
      border-radius: $uni-border-radius-lg;

      .view-item {
        padding: 40rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
      }

      .view-item:last-child {
        border: none;
      }
    }
  }
</style>