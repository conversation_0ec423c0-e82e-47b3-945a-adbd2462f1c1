<template>
  <view class="device-info">
    <view class="base-info">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../static/alarm.svg" class="item-img"></image>
          <span>{{ $t('告警') }}{{ $t('信息') }}</span>
        </view>
        <view v-if="info.state == 1">
          <span class="primary">{{ $t('已处理') }}</span>
        </view>
        <view v-if="info.state == 2">
          <span class="error">{{ $t('未处理') }}</span>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('所属项目') }}</view>
        <view>{{ info.projectName }}</view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('设备名称') }}</view>
        <view>{{ info.deviceName }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('告警对象') }}</view>
        <view>{{ info.name }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('告警名称') }}</view>
        <view class="error">{{ info.alarmName }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('告警代码') }}</view>
        <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
          <view class="u-m-r-5">{{ info.alarmPoint }}</view>
          <u-copy :content="info.alarmPoint" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
            <image src="../../static/copy.png" class="copy-img-width"></image>
          </u-copy>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('告警等级') }}</view>
        <view><u-tag v-if="info.alarmLevel == 1" size="mini" :text="$t('等级一')" type="error" plain></u-tag>
          <u-tag v-if="info.alarmLevel == 2" size="mini" :text="$t('等级二')" type="warning" plain></u-tag>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t( '设备序列号') }}</view>
        <view :class="['flex', 'align-items', 'u-flex-end']" style="text-align: center;">
          <view class="u-m-r-5">{{ info.ac }}</view>
          <u-copy :content="info.ac" :notice="$t('复制成功')" class="flex align-items" style="width: auto;">
            <image src="../../static/copy.png" class="copy-img-width"></image>
          </u-copy>
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('时区') }}</view>
        <view>UTC{{ info.timeZone }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('发生时间') }}</view>
        <view>{{ info.sdt }}
        </view>
      </view>
      <view class="info-item flex justify-content align-items">
        <view>{{ $t('上报时间') }}</view>
        <view>{{ info.createTime }}(UTC+08:00)
        </view>
      </view>
    </view>
    <view class="base-info u-p-b-20">
      <view class="info-item flex justify-content align-items">
        <view class="bold flex align-items">
          <image src="../../static/alarm-method.svg" class="item-img"></image>
          <span>{{ $t('问题分析') }}</span>
        </view>
      </view>
      <template v-if="!isNoAlarmMethod">
        <view class="info-box" v-if="info[getPropFn('reasonsOfProblem')]">
          <view>{{ $t('问题原因') }}：</view>
          <text>{{ info[getPropFn('reasonsOfProblem')] }}</text>
        </view>
        <view class="info-box" v-if="info[getPropFn('alarmInvolvedComponents')]">
          <view>{{ $t('涉及部件') }}：</view>
          <text>{{ info[getPropFn('alarmInvolvedComponents')] }}</text>
        </view>
        <view class="info-box" v-if="info[getPropFn('treatingMethod')]">
          <view>{{ $t('处理办法') }}：</view>
          <text>{{ info[getPropFn('treatingMethod')] }}</text>
        </view>
      </template>
      <template v-else>
        <view class="info-item flex justify-content align-items">
          {{ $t('暂未提供') }}
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    getAlarmInfo,
    getAlarmDetails
  } from '@/api/alarm.js'

  const id = ref()
  onLoad((options) => {
    id.value = options.id
  })
  onShow(() => {
    getInfoFn()
  })

  const info = ref({})
  const getInfoFn = async () => {
    const res = await getAlarmInfo({
      alarmNameId: id.value
    })
    if (res.data.det == "13") {
      res.data.name = `${parseInt(res.data.dc) - 131000 + 1}#Monet-AC`
    } else if (res.data.det == '14') {
      res.data.name = `${parseInt(res.data.dc) - 141000 + 1}#Monet-DC`
    } else if (res.data.det == '15') {
      res.data.name = `${parseInt(res.data.dc) - 151000 + 1}#Monet-STS`
    } else if (res.data.det == '16') {
      // res.data.name = `${parseInt(res.data.dc) - 161000 + 1}#BMS`
      res.data.name = getBmsCluster.value(res.data)
    } else if (res.data.det == '12') {
      res.data.name = uni.$t('本地控制器')
    } else if (res.data.det == '19') {
      if (res.data.alarmPoint == '19029' || res.data.alarmPoint == '19030' || res.data.alarmPoint == '19031' || item
        .alarmPoint == '19022') {
        res.data.name = `${parseInt(res.data.dc) - 191000 + 1}#${uni.$t('充电桩')}_2#${uni.$t('枪')}`
      } else if (res.data.alarmPoint == '19026' || res.data.alarmPoint == '19027' || res.data.alarmPoint ==
        '19028' || res.data
        .alarmPoint == '19011') {
        res.data.name = `${parseInt(res.data.dc) - 191000 + 1}#${uni.$t('充电桩')}_1#${uni.$t('枪')}`
      } else {
        res.data.name = `${parseInt(res.data.dc) - 191000 + 1}#${uni.$t('充电桩')}`
      }
    } else if (item.det == '24') {
      res.data.name = getBmsCluster.value(res.data)
    }
    info.value = res.data
    await getAlarmDetailsFn()
  }
  const isNoAlarmMethod = ref(false)
  const getAlarmDetailsFn = async () => {
    isNoAlarmMethod.value = false
    const res = await getAlarmDetails({
      alarmPoint: info.value.alarmPoint,
      alarmValueIndex: info.value.alarmValueIndex,
      det: info.value.det,
    })
    if (!res.data) {
      isNoAlarmMethod.value = true
      return
    }
    const replaceFn = (str, type) => str?.replace(type == 1 ? /[\n\t]/g : /[\/]/g, type == 1 ? '<br/>' : '、')
    info.value = {
      ...info.value,
      ...res.data,
      createTime: info.value.createTime,
      alarmInvolvedComponents: replaceFn(res.data.alarmInvolvedComponents, 2),
      alarmInvolvedComponentsIt: replaceFn(res.data.alarmInvolvedComponentsIt, 2),
      alarmInvolvedComponentsUs: replaceFn(res.data.alarmInvolvedComponentsUs, 2),
    }
  }
  const getPropFn = computed(() => {
    return (prop) => {
      const lang = uni.cache.getItem('language')
      switch (lang) {
        case 'zh-Hans':
          return prop
        case 'en':
          return prop + 'Us'
        case 'it':
          return prop + 'It'
        default:
          return prop
      }
    }
  })

  const getBmsCluster = computed(() => {
    return ({
      alarmPoint,
      dc,
      det,
      protocolType
    }) => {
      const ALARM_POINT_BASE = 9900
      const DC_OFFSET = det == '16' ? 161000 : 241000
      const BMS_COUNT = 9
      const isBMSBAUCUSTER = det == '24' && protocolType == 20

      // 输入验证
      if (isNaN(Number(alarmPoint)) || isNaN(Number(dc))) {
        throw new Error('Invalid input: alarmPoint and dc must be numbers')
      }

      let alarmPointNum = Number(alarmPoint)
      let clusterIndex = Math.floor((alarmPointNum - ALARM_POINT_BASE) / 10)

      // 边界条件处理
      if (clusterIndex < 0 || clusterIndex >= BMS_COUNT) {
        clusterIndex = BMS_COUNT // 超出范围则默认为 'BMS'
      }

      const dcValue = parseInt(dc) - DC_OFFSET + 1
      return `${dcValue}#${det == '16' ? 'BMS': 'BMS-BAU'}${clusterIndex === BMS_COUNT ? '' : '_' + (isBMSBAUCUSTER ? uni.$t('簇') : '') + (clusterIndex + 1)}`
    }
  })
</script>

<style lang="scss" scoped>
  .device-info {
    padding: 20rpx 0;
    height: 100vh;
    overflow: auto;

    .base-info,
    .group-info {
      background-color: $uni-bg-color;
      border-radius: $uni-border-radius-lg;
      padding: 0 30rpx;
      margin: 0 30rpx 20rpx 30rpx;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $detail-border-color;
      width: 100%;

      .item-img {
        width: 30rpx;
        height: 30rpx;
        margin-right: 10rpx;
      }

      .item-unit {
        font-size: 12px;
        // color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-box {
      margin-top: 30rpx;

      view {
        font-size: 12px;
      }

      text {
        margin-top: 20rpx;
        display: inline-block;
        width: calc(100% - 60rpx);
        background-color: $uni-bg-color-grey;
        padding: 16rpx 30rpx;
        border-radius: 20rpx;
        line-height: 1.5;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }
</style>