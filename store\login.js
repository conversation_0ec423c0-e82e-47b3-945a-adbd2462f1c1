import {
	defineStore
} from 'pinia';
import {
	ref
} from 'vue'
import {
	login,
	getInfo,
	logout,
  updateUserPwd,
  updateUserProfile,
  getRouters
} from '@/api/login.js'

export const useLoginStore = defineStore('login', () => {
	// 登录
	const token = ref()
	const Login = async (userInfo) => {
		const res = await login(userInfo)
    uni.cache.setItem('token', res.token)
		token.value = res.token
    await GetInfo()
    await getRoutersFn()
	}

	// 获取用户信息
	const userInfo = ref({})
	const roles = ref([])
	const permissions = ref([])
	const GetInfo = async () => {
		const res = await getInfo()
		userInfo.value = res.user
		if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
			roles.value = res.roles
			permissions.value = res.permissions
		} else {
			roles.value = ['ROLE_DEFAULT']
		}
    uni.cache.setItem('roles', roles.value)
    uni.cache.setItem('permissions', permissions.value)
    uni.cache.setItem('userInfo', userInfo.value)
		return res
	}

	// 退出系统
	const LogOut = async () => {
		const res = await logout()
		token.value = ''
		roles.value = []
		permissions.value = []
    uni.cache.setItem('roles', [])
    uni.cache.setItem('permissions', [])
    uni.cache.setItem('userInfo', {})
    uni.reLaunch({
      url: '/pages/common/login'
    })
	}

  // 修改密码
  const UpdateUserPwd = async (data) => {
		const res = await updateUserPwd(data)
	}
  // 修改信息
  const UpdateUserProfile = async (data) => {
    const res = await updateUserProfile(data)
  }
  // 获取路由信息
  const routes = ref([])
  const getRoutersFn = async () => {
    const { data } = await getRouters()
    routes.value = data
    uni.cache.setItem('routes', routes.value)
    if (data.findIndex(item => item.name == 'Operation') == -1) {
      uni.setTabBarItem({
        index: 3,
        text: '运维',
        visible: false,
        iconPath: '/static/icon4.png',
        selectedIconPath: '/static/icon4-a.png',
        success: () => {
          console.log('成功了')
        },
        fail: (err) => {
          console.log(err)
        }
      })
    }
  }
  
	return {
		Login,
		LogOut,
		GetInfo,
		userInfo,
		roles,
		token,
		permissions,
    UpdateUserPwd,
    UpdateUserProfile,
    getRoutersFn,
    routes
	};
});