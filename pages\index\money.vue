<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-10-09 10:20:28
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-10-09 15:02:54
 * @FilePath: \elecloud_platform_mobile-main\pages\index\money.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <view class="money">
    <z-paging ref="paging" refresher-only @onRefresh="onRefresh" show-refresher-update-time refresher-update-time-key="money">
      <view class="money-item" v-for="item in sumData.totalIncome" :key="item.currency">
        <view>{{ getSymbolFromCurrency(item.currency) }}</view>
        <span class="item-num">{{ item.income }}</span>
        <span class="color-grey ml-5" style="font-size: 9px">{{ item.currency }}</span>
      </view>
    </z-paging>
  </view>
</template>

<script setup>
import { computed, ref, getCurrentInstance } from 'vue';
import { getSum } from '@/api/home.js';
import { getSymbolFromCurrency } from '@/common/currency.js';

const { proxy } = getCurrentInstance();

const sumData = ref({
  chargeCapacityCalculateSum: 0,
  deviceBatteryCapacitySum: 0,
  deviceRatedPowerSum: 0,
  deviceSum: 0,
  dischargeCapacityCalculateSum: 0,
  photovoltaicPowerCapacityCalculateSum: 0,
  powerPlantSum: 0,
  totalIncome: 0
});
const getSumFn = async () => {
  const res = await getSum();
  let data = res.data;
  if (!data.totalIncome?.length)
    data.totalIncome.push({
      currency: uni.$t('元'),
      income: 0
    });
  data.totalIncome.forEach((item) => {
    item.income = item.income.toFixed(2);
  });
  sumData.value = data;
};
getSumFn();

const onRefresh = async () => {
  await getSumFn();
  proxy.$refs.paging.complete();
};
</script>

<style lang="scss" scoped>
  .money {
    width: 100%;
    height: 100vh;
  }
  .money-item {
    background-color: $uni-bg-color;
    padding: 20rpx 30rpx;
    margin: 20rpx 30rpx 20rpx 30rpx;
    border-radius: $uni-border-radius-lg;
  }
</style>
