<template>
  <view class="user-info">
    <u-navbar :title="$t('个人信息')" leftIconSize="20px" :autoBack="true" :placeholder="true" class="tabbar">
      <template #right>
        <image src="../../static/edit.webp" mode="" style="width: 40rpx;
      height: 40rpx;" @click="handleEditClick"></image>
      </template>
    </u-navbar>
    <view class="flex u-flex-center">
      <view class="flex align-items u-flex-center avatar-bg" @click="handleAvatarClick">
        <u-avatar :src="avatar" size="140"></u-avatar>
        <!-- <image src="../../static/edit-avatar.png" class="avatar-bg-edit"></image> -->
      </view>
    </view>
    <u-form labelPosition="left" :model="form" :rules="rules" ref="formRef" labelWidth="auto">
      <view class="add-item">
        <u-form-item :label="$t('用户名称')" prop="userName" :borderBottom="true">
          <u-input v-model="form.userName" :placeholder="!isEdit ? $t('请输入用户名称'): ''" border="none"
            :readonly="!isEdit" />
        </u-form-item>
        <u-form-item :label="$t('邮箱')" prop="email" :borderBottom="true">
          <u-input v-model="form.email" :placeholder="!isEdit ? $t('请输入邮箱'): ''" border="none" :readonly="!isEdit" />
        </u-form-item>
        <u-form-item :label="$t('手机号码')" prop="phonenumber" :borderBottom="true">
          <u-input v-model="form.phonenumber" :placeholder="!isEdit ? $t('请输入手机号码'): ''" border="none"
            :readonly="!isEdit" />
        </u-form-item>
        <u-form-item :label="$t('创建时间')" :borderBottom="true">
          <u-input v-model="form.createTime" border="none" readonly></u-input>
        </u-form-item>
      </view>
    </u-form>
    <u-button type="primary" style="border-radius: 100rpx;" class="u-m-t-40" @click="handleConfirmClick"
      v-if="isEdit">{{ $t('确认') }}</u-button>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    getUserProfile,
    updateUserProfile
  } from '@/api/login.js'
  import {
    onShow
  } from '@dcloudio/uni-app'
  import {
    config
  } from '@/common/request.js'
  import {
    checkRole
  } from '@/common/utils.js'

  /**
   * 获取用户信息
   */
  const userInfo = ref({})
  const roleGroup = ref()
  const avatar = ref()
  const isAdmin = ref(checkRole(['admin']))
  const getUserProfileFn = async () => {
    const res = await getUserProfile()
    userInfo.value = res.data
    roleGroup.value = res.roleGroup
    changeAvatar()
    form.value = res.data
  }
  const changeAvatar = () => {
    let Czh = '/profile/avatar/2024/08/29/blob_20240829184919A002.png'
    let Cen = '/profile/avatar/2024/08/29/blob_20240829181556A001.png'
    let Een = '/profile/avatar/2024/08/29/blob_20240829185325A003.png'
    let Ezh = '/profile/avatar/2024/08/29/blob_20240829181917A002.png'
    if (isAdmin.value) {
      if (uni.cache.getItem('language') == 'zh-Hans') {
        avatar.value =
          `${config.baseURL}profile${uni.cache.getItem('service') == 'en' ? Ezh.split('profile')[1]: Czh.split('profile')[1]}`
      } else {
        avatar.value =
          `${config.baseURL}profile${uni.cache.getItem('service') == 'en' ? Een.split('profile')[1]: Cen.split('profile')[1]}`
      }
    } else {
      avatar.value = `${config.baseURL}profile${userInfo.value.avatar.split('profile')[1]}`
    }
  }

  /**
   * 修改头像
   */
  const handleAvatarClick = () => {

  }

  /**
   * 修改信息
   */
  const isEdit = ref(false)
  const handleEditClick = () => {
    isEdit.value = !isEdit.value
  }
  const formRef = ref(null)
  const form = ref({
    userName: '',
    email: '',
    phonenumber: ''
  })
  const rules = ref({
    'userName': [{
      type: 'string',
      required: true,
      message: uni.$t('请输入用户名称'),
      trigger: ['blur', 'change'],
    }, {
      pattern: /^[^\u4e00-\u9fa5]+$/,
      message: uni.$t('不允许有中文字符'),
      trigger: 'blur'
    }],
    'email': [{
      type: 'email',
      message: uni.$t('请输入正确的邮箱'),
      trigger: ['blur', 'change'],
    }],
    'phonenumber': [{
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: uni.$t('请输入正确的手机号码'),
      trigger: 'blur'
    }]
  })

  const handleConfirmClick = () => {
    if (formRef.value) {
      formRef.value.validate().then(valid => {
        if (valid) {
          updateUserProfile({
            userName: form.value.userName,
            email: form.value.email,
            phonenumber: form.value.phonenumber
          }).then(res => {
            uni.$u.toast(uni.$t('修改成功'))
            isEdit.value = false
            getUserProfileFn()
          }).catch(() => {
            uni.$u.toast(uni.$t('修改失败'))
            getUserProfileFn()
          })
        }
      })
    }
  }

  onShow(() => {
    getUserProfileFn()
  })
</script>

<style lang="scss" scoped>
  .user-info {
    padding: 60rpx 30rpx 0 30rpx;
    height: 100vh;

    .avatar-bg {
      width: 150rpx;
      height: 150rpx;
      background-color: #fff;
      border-radius: 50%;
      position: relative;

      &-edit {
        width: 36rpx;
        height: 30rpx;
        position: absolute;
        right: 10rpx;
        bottom: 10rpx;
      }
    }

    .add-item {
      background-color: #fff;
      padding: 0 30rpx;
      border-radius: 6px;
      margin-top: 60rpx;
    }
  }

  :deep(.u-input__content__field-wrapper__field) {
    text-align: right !important;
  }

  :deep(.u-line) {
    border-bottom: 1px solid $detail-border-color !important;
  }

  :deep(.u-form) {
    .u-form-item__body__left__content__label {
      font-size: 14px
    }
  }
</style>