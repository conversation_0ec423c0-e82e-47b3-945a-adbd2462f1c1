<template>
  <view style="height: 100vh;padding: 20rpx 30rpx;overflow-y: auto;">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="setting1900" :label="$t('运行模式')" required @click="handleCellClick('mode')">
          <view>
            {{ slectModeText }}
          </view>
        </u-form-item>
        <u-form-item prop="setting1924" :label="$t('执行周期')" required v-if="!isEmpty(form.setting1924) && form.setting1900 == 1">
          <u-radio-group v-model="form.setting1924" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('日')" name="0" class="mr-20"></u-radio>
            <u-radio :label="$t('周')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('月')" name="2"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="cutTopId" :label="$t('削峰填谷')" required @click="handleCellClick('jfpg')"
          v-if="form.setting1900 == 1 && isJFPGType == 0">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ slectJfpgText }}
          </view>
        </u-form-item>
        <template v-if="form.setting1900 == 1 && isJFPGType == 1">
          <u-form-item :label="$t('削峰填谷')" required >
          </u-form-item>
          <u-form-item v-for="(item, index) in form.weeks" :key="item.label" prop="cutTopId" :label="item.label"
            required @click="handleCellClick('jfpg', index)">
            <view class="u-line-1" style="width: 60%;text-align: right;">
              {{ item.jfpgText ? item.jfpgText: '--' }}
            </view>
          </u-form-item>
        </template>
        <template v-if="form.setting1900 == 1 && isJFPGType == 2">
          <u-form-item :label="$t('削峰填谷')" required >
          </u-form-item>
          <u-form-item v-for="(item, index) in form.month" :key="item.label" prop="cutTopId" :label="item.label"
            required @click="handleCellClick('jfpg', index)">
            <view class="u-line-1" style="width: 60%;text-align: right;">
              {{ item.jfpgText ? item.jfpgText: '--' }}
            </view>
          </u-form-item>
        </template>
        <u-form-item prop="reservePatternId" :label="$t('备电方案')" required @click="handleCellClick('backup')"
          v-if="form.setting1900 == 2">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ slectBuckupText }}
          </view>
        </u-form-item>
        <u-form-item prop="electricPriceId" :label="$t('分时电价')" required @click="handleCellClick('price')" v-if="isJFPGType == 0">
          <view class="u-line-1" style="width: 60%;text-align: right;">
            {{ slectPriceText }}
          </view>
        </u-form-item>
        <template v-if="isJFPGType == 1">
          <u-form-item :label="$t('分时电价')" required >
          </u-form-item>
          <u-form-item v-for="(item, index) in form.weeks" :key="item.label" prop="electricPriceId" :label="item.label"
            required @click="handleCellClick('price', index)">
            <view class="u-line-1" style="width: 60%;text-align: right;">
              {{ item.priceText ? item.priceText: '--' }}
            </view>
          </u-form-item>
        </template>
        <template v-if="isJFPGType == 2">
          <u-form-item :label="$t('分时电价')" required >
          </u-form-item>
          <u-form-item v-for="(item, index) in form.month" :key="item.label" prop="electricPriceId" :label="item.label"
            required @click="handleCellClick('price', index)">
            <view class="u-line-1" style="width: 60%;text-align: right;">
              {{ item.priceText ? item.priceText: '--' }}
            </view>
          </u-form-item>
        </template>
        <u-form-item prop="setting1917" :label="$t('防逆流使能')" required v-if="form.setting1900 == 0 || form.setting1900 == 1 || form.setting1900 == 2">
          <u-radio-group v-model="form.setting1917" placement="row" size="16px"
            style="display: flex;justify-content: flex-end;">
            <u-radio :label="$t('使能')" name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('不使能')" name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="setting1898" :label="$t('变压器容量')" required v-if="form.setting1900 == 3">
          <u-number-box v-model="form.setting1898" :step="0.1" :min="0" :max="5000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
      </u-form>
    </view>
    <view
      style="margin-top: 20rpx;font-size: 12px;padding: 20rpx 30rpx;background-color: #fff;border-radius: 6px;color: #b7b7b7;">
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('下发状态') }}：
        </view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;" v-if="form.setting1900 == 1">
          {{ $t('执行周期') }}：
        </view>
        <view>{{ $t('日') }}：{{ $t('每天') }}; {{ $t('月') }}：{{ $t('每周（1~7天）') }}; {{ $t('年') }}：{{ $t('1~12个月') }}</view>
        <view>{{ $t('注：该执行周期只适用于削峰填谷。') }}</view>
      </view>
      <view style="margin-bottom: 10rpx;" v-if="form.setting1900 == 1">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('削峰填谷') }}：
        </view>
        <view>{{ $t('按照设置时间段给系统充放电。') }}</view>
      </view>
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('分时电价') }}：
        </view>
        <view>{{ $t('用于电网计算收益。') }}</view>
      </view>
      <view v-if="form.setting1900 == 0 || form.setting1900 == 1 || form.setting1900 == 2">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('防逆流使能') }}：
        </view>
        <view>{{ $t('防止系统放电馈入电网。') }}</view>
      </view>
      <view v-if="form.setting1900 == 3">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('变压器容量') }}：
        </view>
        <view>{{ $t('变压器容量。') }}</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx;border-radius: 6px;" @click="handleSendClick"
      :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey"
      style="width: 100%;display: flex; align-items: center;justify-content: center;" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx;margin-top: 1rpx;"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>

    <u-picker :show="isShowSelect" :defaultIndex="defaultSelectIndex" keyName="text" :columns="selectCom"
      itemHeight="88" @confirm="handleSelectConfirm" :closeOnClickOverlay="true" @close="handleSelectCancel"
      @cancel="handleSelectCancel" :cancelText="$t('取消')" :confirmText="$t('确认')"></u-picker>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed,
    toRefs,
    watch
  } from 'vue';
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    onShow,
    onLoad
  } from '@dcloudio/uni-app'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { isEmpty } from 'lodash'
  import { paramLabelStyle, inputNumberStyle } from '@/constant'

  const paramStore = useParamStore()
  const {
    routeQuery,
    control,
    groupControl,
    baseInfo
  } = toRefs(useMonitorStore())
  const {
    proxy
  } = getCurrentInstance()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  const current = ref()
  onLoad(async (options) => {
    current.value = options.index
    await useParamStore().allJfpgFn()
    await useParamStore().allPriceFn()
    await useParamStore().allBackUpFn()
    getInfo()
  })

  // 使用 reactive 创建响应式状态  
  const form = ref({
    status: 0,
    setting1900: '',
    setting1917: "1",
    cutTopId: '',
    electricPriceId: '',
    reservePatternId: undefined,
    weeks: [{
        label: uni.$t('星期一'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期二'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期三'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期四'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期五'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期六'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('星期天'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      }
    ],
    setting1924: '',
    month: [{
        label: uni.$t('一月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('二月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('三月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('四月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('五月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('六月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('七月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('八月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('九月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('十月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('十一月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
      {
        label: uni.$t('十二月'),
        electricPriceId: undefined,
        priceText: undefined,
        cutTopId: undefined,
        jfpgText: undefined
      },
    ],
    setting1898: undefined
  })
  const rules = ref({
    setting1917: {
      type: 'string',
      required: true,
      message: uni.$t('请选择'),
      trigger: ['blur', 'change'],
    },
  })
  const formRef = ref(null);
  const handleSendClick = () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    // 协议版本 BV04
    let electricPriceIds = null
    let cutTopIds = null
    if (isJFPGType.value == 0) {
      electricPriceIds = form.value.electricPriceId
      cutTopIds = form.value.cutTopId
    } else if (isJFPGType.value == 1) {
      electricPriceIds = form.value.weeks.map(item => item.electricPriceId).join(',')
      cutTopIds = form.value.weeks.map(item => item.cutTopId).join(',')
    } else if (isJFPGType.value == 2) {
      electricPriceIds = form.value.month.map(item => item.electricPriceId).join(',')
      cutTopIds = form.value.month.map(item => item.cutTopId).join(',')
    }
    if (formRef.value) {
      formRef.value.validate().then(async valid => {
        if (valid) {
          try {
            await paramStore.sendParamSystemFn({
              ac: ac,
              id: form.value.id,
              ac: form.value.ac,
              setting1900: form.value.setting1900,
              setting1917: form.value.setting1917,
              cutTopId: cutTopIds,
              electricPriceId: electricPriceIds,
              reservePatternId: form.value.reservePatternId,
              setting1924: form.value.setting1924,
              setting1898: form.value.setting1898,
            })
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发成功'))
            }, 20)
            getInfo()
          } catch (e) {
            console.log(e)
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发失败'))
            }, 20)
            getInfo()
          }
        }
      })
    }
  }

  const isSend = computed(() => {
    if (isGroup.value) {
      let ac = routeQuery.value.groupId[current.value]
      let value = groupControl.value.find(item => item.ac == ac)
      if (!Object.keys(value).length) return true
      return value['onLineState'] == '离线'
    } else {
      if (!Object.keys(control.value).length) return true
      return control.value['onLineState'] == '离线'
    }
  })
  
  const isJFPGType = computed(() => {
    if (!isEmpty(form.value.setting1924)) {
      if (form.value.setting1924 == '1') return 1
      else if (form.value.setting1924 == '0') return 0
      else if (form.value.setting1924 == '2') return 2
    } else {
      if (baseInfo.value.deviceUpdateFactoryAgreement == 'BV04') return 1
      else return 0
    }
  })
  watch(() => form.value.setting1900, () => {
    if (form.value.setting1900 != '1') {
      if (isEmpty(form.value.setting1924)) form.value.setting1924 = null
      else form.value.setting1924 = '0'
    }
  })
  const getInfo = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    const res = await paramStore.systemInfoFn({
      ac
    })
    if (!res) return
    let electricPriceId = null
    let cutTopId = null
    if (!isEmpty(res.setting1924)) {
      if (res.setting1924 == '0') {
        electricPriceId = res.electricPriceId ? Number(res.electricPriceId) : null
        cutTopId = res.cutTopId ? Number(res.cutTopId) : null
      } else if (res.setting1924 == '1') {
        let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
        electricPriceIds.forEach((item, index) => {
          form.value.weeks[index].electricPriceId = item ? Number(item) : undefined
          form.value.weeks[index].priceText = item ? paramStore.priceOptions.find(item1 => item1.id == Number(
            item))?.name : '--'
        })
        let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
        cutTopIds.forEach((item, index) => {
          form.value.weeks[index].cutTopId = item ? Number(item) : undefined
          form.value.weeks[index].jfpgText = item ? paramStore.jfpgOptions.find(item1 => item1.id == Number(item))
            ?.title : '--'
        })
      } else if (res.setting1924 == '2') {
        let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
        electricPriceIds.forEach((item, index) => {
          form.value.month[index].electricPriceId = item ? Number(item) : undefined
          form.value.month[index].priceText = item ? paramStore.priceOptions.find(item1 => item1.id == Number(
            item))?.name : '--'
        })
        let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
        cutTopIds.forEach((item, index) => {
          form.value.month[index].cutTopId = item ? Number(item) : undefined
          form.value.month[index].jfpgText = item ? paramStore.jfpgOptions.find(item1 => item1.id == Number(item))
            ?.title : '--'
        })
      }
    } else {
      if (baseInfo.value.deviceUpdateFactoryAgreement == 'BV04') {
        let electricPriceIds = res.electricPriceId ? res.electricPriceId.split(',') : []
        electricPriceIds.forEach((item, index) => {
          form.value.weeks[index].electricPriceId = item ? Number(item) : undefined
          form.value.weeks[index].priceText = item ? paramStore.priceOptions.find(item1 => item1.id == Number(
            item))?.name : '--'
        })
        let cutTopIds = res.cutTopId ? res.cutTopId.split(',') : []
        cutTopIds.forEach((item, index) => {
          form.value.weeks[index].cutTopId = item ? Number(item) : undefined
          form.value.weeks[index].jfpgText = item ? paramStore.jfpgOptions.find(item1 => item1.id == Number(item))
            ?.title : '--'
        })
      } else {
        electricPriceId = res.electricPriceId ? Number(res.electricPriceId) : null
        cutTopId = res.cutTopId ? Number(res.cutTopId) : null
      }
    }
    let data = JSON.parse(JSON.stringify(form.value))
    if (res.status == 1 && data.status == 2) {
      uni.$u.toast(uni.$t('下发成功'))
    } else if (res.status == 3 && data.status == 2) {
      uni.$u.toast(uni.$t('下发失败'))
    }
    form.value = {
      ...form.value,
      ...res,
      electricPriceId,
      cutTopId
    }
  }


  /**
   * 选择
   */
  const isShowSelect = ref(false)
  const defaultSelectIndex = ref([0])
  const selectType = ref('mode')
  const modeOptions = ref([{
      text: uni.$t('手动模式'),
      value: '0'
    },
    {
      text: uni.$t('削峰填谷'),
      value: '1'
    },
    {
      text: uni.$t('后备模式'),
      value: '2'
    },
    {
      text: uni.$t('动态扩容'),
      value: '3'
    },
    // {
    //   text: uni.$t('光伏消纳'),
    //   value: '4'
    // },
  ])
  const slectModeText = computed(() => {
    return modeOptions.value.find(item => item.value == form.value.setting1900)?.text
  })
  const slectJfpgText = computed(() => {
    return paramStore.jfpgOptions.find(item => item.id == form.value.cutTopId)?.title
  })
  const slectPriceText = computed(() => {
    return paramStore.priceOptions.find(item => item.id == form.value.electricPriceId)?.name
  })
  const slectBuckupText = computed(() => {
    return paramStore.backupOptions.find(item => item.id == form.value.reservePatternId)?.name
  })
  const selectCom = computed(() => {
    if (selectType.value == 'mode') {
      return [modeOptions.value]
    }
  })
  const handleSelectConfirm = ({
    value
  }) => {
    if (selectType.value == 'mode') {
      form.value.setting1900 = value[0].value
    }
    isShowSelect.value = false
  }
  const handleSelectCancel = () => {
    isShowSelect.value = false
  }
  const selectIndex = ref()
  const handleCellClick = (type, index) => {
    selectIndex.value = index
    defaultSelectIndex.value = [form.value.setting1900]
    selectType.value = type
    if (type == 'mode') {
      isShowSelect.value = true
    } else if (type == 'jfpg') {
      if (isJFPGType.value == 1) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.weeks[index].cutTopId}`
        })
      } else if (isJFPGType.value == 0) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.cutTopId}`
        })
      } else if (isJFPGType.value == 2) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.month[index].cutTopId}`
        })
      }
    } else if (type == 'price') {
      if (isJFPGType.value == 1) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.weeks[index].electricPriceId}`
        })
      } else if (isJFPGType.value == 0) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.electricPriceId}`
        })
      } else if (isJFPGType.value == 2) {
        uni.navigateTo({
          url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.month[index].electricPriceId}`
        })
      }
    } else if (type == 'backup') {
      uni.navigateTo({
        url: `/pages/monitor/param/schemeList?type=${type}&id=${form.value.reservePatternId}`
      })
    }
  }
  uni.$on('schemeItemClick', (data) => {
    if (!data) return
    if (selectType.value == 'jfpg') {
      if (isJFPGType.value == 1) {
        form.value.weeks[selectIndex.value].cutTopId = data.id
        form.value.weeks[selectIndex.value].jfpgText = paramStore.jfpgOptions.find(item1 => item1.id == data.id)
          ?.title
      } else if (isJFPGType.value == 0) {
        form.value.cutTopId = data.id
      } else if (isJFPGType.value == 2) {
        form.value.month[selectIndex.value].cutTopId = data.id
        form.value.month[selectIndex.value].jfpgText = paramStore.jfpgOptions.find(item1 => item1.id == data.id)
          ?.title
      }
    } else if (selectType.value == 'price') {
      if (isJFPGType.value == 1) {
        form.value.weeks[selectIndex.value].electricPriceId = data.id
        form.value.weeks[selectIndex.value].priceText = paramStore.priceOptions.find(item1 => item1.id == data.id)
          ?.name
      } else if (isJFPGType.value == 0) {
        form.value.electricPriceId = data.id
      } else if (isJFPGType.value == 2) {
        form.value.month[selectIndex.value].electricPriceId = data.id
        form.value.month[selectIndex.value].priceText = paramStore.priceOptions.find(item1 => item1.id == data.id)
          ?.name
      }
    } else if (selectType.value == 'backup') {
      form.value.reservePatternId = data.id
    }
  })
</script>

<style scoped lang="scss">
  .send-status {
    display: flex;
    align-items: center;

    image {
      margin-right: 6rpx;
    }
  }

  .param-form {
    width: 100%;
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>