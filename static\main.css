/* ==================
         模态窗口
 ==================== */

.zy-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 1110;
	opacity: 0;
	outline: 0;
	text-align: center;
	-ms-transform: scale(1.185);
	transform: scale(1.185);
	backface-visibility: hidden;
	perspective: 2000upx;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
}

.zy-modal::before {
	content: "\200B";
	display: inline-block;
	height: 100%;
	vertical-align: middle;
}

.zy-modal.show {
	opacity: 1;
	transition-duration: 0.3s;
	-ms-transform: scale(1);
	transform: scale(1);
	overflow-x: hidden;
	overflow-y: auto;
	pointer-events: auto;
}

.zy-dialog {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin-left: auto;
	margin-right: auto;
	width: 640upx;
	max-width: 100%;
	background-color: #f8f8f8;
	border-radius: 10upx;
	overflow: hidden;
  border-radius: 20px;
}
.zy-modal .zy-dialog>.zy-bar:first-child .action{
  min-width: 100rpx;
  margin-right: 0;
  min-height: 100rpx;
}

/* ==================
         进度条
 ==================== */

.zy-progress {
	overflow: hidden;
	height: 28upx;
	background-color: #ebeef5;
	display: inline-flex;
	align-items: center;
	width: 100%;
}

.zy-progress+view,
.zy-progress+text {
	line-height: 1;
}

.zy-progress view {
	width: 0;
	height: 100%;
	align-items: center;
	display: flex;
	justify-items: flex-end;
	justify-content: space-around;
	font-size: 20upx;
	color: #ffffff;
	transition: width 0.6s ease;
}

.zy-progress text {
	align-items: center;
	display: flex;
	font-size: 20upx;
	color: #333333;
	text-indent: 10upx;
}

.zy-progress.striped view {
	background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
	background-size: 72upx 72upx;
}

.zy-progress.active view {
	animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
	from {
		background-position: 72upx 0;
	}

	to {
		background-position: 0 0;
	}
}

/*  -- 内外边距 -- */
.padding-xl {
	padding: 50upx;
}
.padding-top {
	padding-top: 30upx;
}

/* ==================
          操作条
 ==================== */

.zy-bar {
	display: flex;
	position: relative;
	/* align-items: center; */
	min-height: 100upx;
	justify-content: center
}

.zy-bar .action {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: center;
	max-width: 100%;
}

.zy-bar .action:first-child {
	margin-left: 30upx;
	font-size: 30upx;
}

.zy-bar .action text.text-cut {
	text-align: left;
	width: 100%;
}

.zy-bar .zy-avatar:first-child {
	margin-left: 20upx;
}

.zy-bar .action:first-child>text[class*="cuIcon-"] {
	margin-left: -0.3em;
	margin-right: 0.3em;
}

.zy-bar .action:last-child {
	margin-right: 30upx;
}

.zy-bar .action>text[class*="cuIcon-"],
.zy-bar .action>view[class*="cuIcon-"] {
	font-size: 36upx;
}

.zy-bar .action>text[class*="cuIcon-"]+text[class*="cuIcon-"] {
	margin-left: 0.5em;
}
/* ==================
          布局
 ==================== */

/*  -- flex弹性布局 -- */

.flex-wrap {
	flex-wrap: wrap;
}
/* ==================
          背景
 ==================== */
.bg-blue {
	background-color: #0081ff;
	color: #ffffff;
}
.bg-white {
	background-color: #ffffff;
	color: #666666;
}
.bg-blue.light {
	color: #0081ff;
	background-color: #cce6ff;
}

.bg-gradual-blue {
	background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
	color: #ffffff;
}
/* ==================
          文本
 ==================== */

.text-center {
	text-align: center;
}

.text-blue,
.line-blue,
.lines-blue {
	color: #0081ff;
}

.text-white,
.line-white,
.lines-white {
	color: #ffffff;
}
