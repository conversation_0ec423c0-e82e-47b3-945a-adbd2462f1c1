<template>
  <view class="profile">
    <u-navbar leftText="返回" :title="$t('tabbar.tabbar4')" bgColor="rgba(0,0,0,0)">
      <!-- <template #right>
        <image src="../../static/bell1.png" mode="" class="top-add" @click="handleAddClick"></image>
      </template> -->
    </u-navbar>
    <view class="top">
      <view class="top-img flex u-flex-between" @click="handleUserClick">
        <view class="flex">
          <view class="flex align-items u-flex-center"
            style="width: 150rpx;height: 150rpx;background-color: #fff;border-radius: 50%;">
            <u-avatar :src="avatar" size="140"></u-avatar>
          </view>
          <view class="top-ri u-m-l-20 u-flex align-items">
            <view class="name">{{ userInfo?.userName }}</view>
          </view>
        </view>
        <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
      </view>
    </view>
    <view class="profile-view">
      <view class="view-item flex justify-content align-items" @click="handleChangePassword">
        <view class="flex justify-content align-items">
          <!-- <u-icon name="lock" size="38" class="item-img"></u-icon> -->
          <image src="../../static/password.png" class="item-img"></image>
          <view>{{ $t('changePassword') }}</view>
        </view>
        <view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
      <view class="view-item flex justify-content align-items" @click="handleNoticeClick()">
        <view class="flex justify-content align-items">
          <image src="../../static/bell.png" class="item-img"></image>
          <view>{{ $t('公告') }}</view>
        </view>
        <view class="flex ft12">
          <text class="notice-value" v-if="noticeStore.unreadCount > 0">{{ noticeStore.unreadCount }}</text>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
      <view class="view-item flex justify-content align-items" @click="handleLanguageClick('lang')">
        <view class="flex justify-content align-items">
          <image src="../../static/lanauge.png" class="item-img"></image>
          <view>{{ $t('语言') }}</view>
        </view>
        <view class="flex ft12">
          <view>
            {{ currentLanguage }}
          </view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
      <view class="view-item flex justify-content align-items" @click="handleLanguageClick('service')"
        v-if="serviceArr.length > 1">
        <view class="flex justify-content align-items">
          <image src="../../static/server.png" class="item-img"></image>
          <view>{{ $t('服务器') }}</view>
        </view>
        <view class="flex ft12">
          <view>
            {{ currentService }}
          </view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
      <view class="view-item flex justify-content align-items" @click="handleClearClick">
        <view class="flex justify-content align-items">
          <image src="../../static/clear.png" class="item-img"></image>
          <view>{{ $t('清空缓存') }}</view>
        </view>
        <view class="flex ft12">
          <view>{{ currentSize }}</view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
      <view class="view-item flex justify-content align-items" @click="handleAbout">
        <view class="flex justify-content align-items">
          <image src="../../static/about.png" class="item-img"></image>
          <view>{{ $t('关于APP') }}</view>
        </view>
        <view class="flex ft12">
          <text class="u-slot-value" v-if="isShowNew">{{ $t('新') }}</text>
          <view v-else>{{ `${$t('版本')} ${nowVersion}` }}</view>
          <u-icon name="arrow-right" class="u-m-l-10"></u-icon>
        </view>
      </view>
    </view>

    <view class="profile-bo flex align-items" @click="handleLogoutClick">
      <image src="../../static/out.png" class="item-img" style="width: 38rpx;height: 38rpx;"></image>
      {{ $t('退出登录') }}
    </view>

    <view class="profile-bottom" @click="handlePhone" :style="{ bottom: `calc(30rpx + ${style.paddingBottom})` }">
      {{ $t('联系我们') }}：+86 0755-23051586
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    getCurrentInstance,
    computed
  } from 'vue'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    languageArr
  } from '@/locale/index.js'
  import {
    init as initLang
  } from '@/locale/index'
  import {
    getUserProfile
  } from '@/api/login.js'
  import {
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    config,
    initService
  } from '@/common/request.js'
  import otherColor from '../../common/other.module.scss'
  import {
    formatSizeUnits,
    checkRole
  } from '@/common/utils.js'
  import {
    getUpdradeInfo
  } from '@/api/upgrade.js'
  import {
    useNoticeStore
  } from '@/store/notice'

  const loginStore = useLoginStore()
  const {
    proxy
  } = getCurrentInstance()

  /**
   * 退出登录
   */
  const handleLogoutClick = () => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定退出系统吗？'),
      confirmColor: otherColor.primaryColor,
      success: async (res) => {
        if (res.confirm) {
          await loginStore.LogOut()
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }

  /**
   * 切换语言
   */
  const actionName = ref('')
  const currentService = ref('')
  const serviceArr = ref([])
  const initLanguage = () => {
    serviceArr.value = JSON.parse(import.meta.env.VITE_SERVICE_ARR).map(item => {
      if (item.value == 'zh' || item.value == 'en' || item.value == 'test') {
        item.label = uni.$t(item.label)
      }
      return item
    })
    currentService.value = serviceArr.value.find(item => item.value == uni.cache.getItem('service')).label
  }
  initService()
  initLanguage()
  currentService.value = serviceArr.value.find(item => item.value == uni.cache.getItem('service')).label
  const handleLanguageClick = (type) => {
    actionName.value = type
    uni.navigateTo({
      url: `/pages/common/selectTool?actionName=${type}&type=${1}`
    })
  }
  uni.$on('toolClick', (data) => {
    if (!data) return
    if (actionName.value == 'lang') {
      let sys = uni.$u.sys().platform
      if (sys == 'ios') {
        uni.setTabBarItem({
          index: 0,
          text: uni.$t('tabbar.tabbar1'),
          success: () => {
            console.log('cehngg')
          },
          fail: (err) => {
            console.log('fail', err)
          }
        })
        uni.setTabBarItem({
          index: 1,
          text: uni.$t('tabbar.tabbar2'),
        })
        uni.setTabBarItem({
          index: 2,
          text: uni.$t('tabbar.tabbar3'),
        })
        uni.setTabBarItem({
          index: uni.cache.getItem('unreadTabbarIndex'),
          text: uni.$t('tabbar.tabbar4'),
        })
      }
      currentLanguage.value = data.label
    } else {
      currentService.value = data.label
    }
    initLanguage()
    changeAvatar()
  })
  onUnload(() => {
    uni.$off('toolClick')
  })
  const currentLanguage = ref()
  currentLanguage.value = languageArr.find(item => item.value == uni.cache.getItem('language')).label

  /**
   * 清缓存
   */
  const handleClearClick = () => {
    uni.showModal({
      title: uni.$t('系统提示'),
      content: uni.$t('确定清空所有缓存，包括登录状态，记住密码等？'),
      confirmColor: otherColor.primaryColor,
      success: (res) => {
        if (res.confirm) {
          uni.clearStorageSync()
          initLang()
          initService()
          uni.reLaunch({
            // url: '/pages/profile/index-copy'
            url: '/pages/common/login'
          })
          uni.$u.toast(uni.$t('清空成功'))
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    })
  }
  const currentSize = formatSizeUnits(uni.getStorageInfoSync().currentSize)
  /**
   * 获取用户信息
   */
  const userInfo = ref({})
  const roleGroup = ref()
  const avatar = ref()
  const isAdmin = ref(checkRole(['admin']))
  const getUserProfileFn = async () => {
    const res = await getUserProfile()
    userInfo.value = res.data
    roleGroup.value = res.roleGroup
    changeAvatar()
  }
  const changeAvatar = () => {
    let Czh = '/profile/avatar/2024/08/29/blob_20240829184919A002.png'
    let Cen = '/profile/avatar/2024/08/29/blob_20240829181556A001.png'
    let Een = '/profile/avatar/2024/08/29/blob_20240829185325A003.png'
    let Ezh = '/profile/avatar/2024/08/29/blob_20240829181917A002.png'
    if (isAdmin.value) {
      if (currentLanguage.value == '简体中文') {
        avatar.value =
          `${config.baseURL}profile${currentService.value == uni.$t('国际站') ? Ezh.split('profile')[1]: Czh.split('profile')[1]}`
      } else {
        avatar.value =
          `${config.baseURL}profile${currentService.value == uni.$t('国际站') ? Een.split('profile')[1]: Cen.split('profile')[1]}`
      }
    } else {
      avatar.value = `${config.baseURL}profile${userInfo.value.avatar.split('profile')[1]}`
    }
  }

  /**
   * 修改密码
   */
  const handleChangePassword = () => {
    uni.navigateTo({
      url: '/pages/profile/editPwd'
    })
  }

  /**
   * 关于App
   */
  const nowVersion = ref()
  // #ifdef APP-PLUS
  nowVersion.value = uni.$u.sys().appWgtVersion
  // #endif
  // #ifdef H5
  nowVersion.value = uni.$u.sys().appVersion
  // #endif
  const handleAbout = () => {
    uni.navigateTo({
      url: '/pages/profile/about-app'
    })
  }
  const isShowNew = ref(false)
  const getUpdradeInfoFn = async () => {
    const res = await getUpdradeInfo({
      version: nowVersion.value,
      name: uni.$u.sys().appName,
      platform: uni.$u.sys().platform
    })
    if (res.msg == '应用程序需要更新') {
      isShowNew.value = true
    } else {
      isShowNew.value = false
    }
  }

  /**
   * 联系我们
   */
  const handlePhone = () => {
    uni.makePhoneCall({
      phoneNumber: '+86 0755-23051586'
    })
  }

  /**
   * 用户头像
   */
  const handleUserClick = () => {
    uni.navigateTo({
      url: '/pages/profile/userInfo'
    })
  }

  /**
   * 公告
   */
  const noticeStore = useNoticeStore()
  const handleNoticeClick = () => {
    // 跳转到公告列表页面
    uni.navigateTo({
      url: '/pages/profile/notice/index'
    })
  }

  onShow(() => {
    getUserProfileFn()
    getUpdradeInfoFn()
    noticeStore.getUnreadNoticeCountFn()
  })

  /**
   * h5、app
   */
  const style = computed(() => {
    const obj = {}
    // #ifdef APP-PLUS
    obj.paddingBottom = '0'
    // #endif
    // #ifdef H5
    obj.paddingBottom = '50px'
    // #endif

    return obj
  })
</script>

<style scoped lang="scss">
  .hover-bg {
    background-color: #000;
  }

  .profile {
    width: 100%;
    height: 100vh;

    .top-add {
      width: 40rpx;
      height: 40rpx;
    }

    .top {
      height: 400rpx;
      display: flex;
      align-items: flex-end;
      padding: 50rpx 60rpx;
      position: relative;
      padding-right: 30rpx;

      .top-img {
        width: 100%;
        padding-right: 50rpx;
      }
    }

    .name {
      font-size: 18px;
      font-weight: bold;
      margin-top: 20rpx;
      margin-bottom: 20rpx;
    }

    .profile-view {
      background-color: #fff;
      padding: 0 50rpx;
      margin: 0 30rpx;
      border-radius: 30rpx;

      .view-item {
        padding: 40rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
      }

      .notice-value {
        line-height: 17px;
        text-align: center;
        font-size: 10px;
        padding: 0 5px;
        height: 17px;
        color: #fff;
        border-radius: 100px;
        background-color: #f56c6c;
        margin-left: auto;
      }

      .view-item:last-child {
        border: none;
      }
    }

    .profile-bo {
      background-color: #fff;
      padding: 30rpx 0;
      margin: 0 40rpx;
      font-weight: bold;
      margin-top: 30rpx;
      justify-content: center;
      border-radius: 100rpx;
    }

    .profile-bottom {
      position: fixed;
      bottom: 30rpx;
      width: 100%;
      text-align: center;
      font-size: 14px;
      color: $uni-text-color-grey;
    }

    .item-img {
      margin-right: 10rpx;
      width: 34rpx;
      height: 34rpx;
    }
  }

  :deep(.u-navbar__content__left) {
    display: none;
  }
</style>