export const projectList = (queryInfo) => uni.$u.http.get('/system/project/list', {
  params: queryInfo,
  custom: {
      loading: false
    }
})

// 获取项目详情
export const getProjectInfo = (queryInfo) => uni.$u.http.get(`/system/project/${queryInfo.projectId}`)

// 新增项目
export const addProject = (data) => uni.$u.http.post('/system/project', data)

// 修改项目
export const editProject = (data) => uni.$u.http.put('/system/project', data)

// 删除项目
export const deleteProject = (queryInfo) => uni.$u.http.delete(`/system/project/${queryInfo.projectIds}`)

// 获取全部项目
export const allProject = (queryInfo) => uni.$u.http.get('/system/project/list2')