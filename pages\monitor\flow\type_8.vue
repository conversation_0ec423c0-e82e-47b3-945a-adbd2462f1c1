<template>
  <view class="flow-item u-flex u-flex-center align-items bold">
    <view class="item-le">
      <view class="le-gird flex u-flex-column align-items">
        <image src="../../../static/flow_ac.png" mode=""></image>
        <view class="ml-5">{{ flowData.power }} kW</view>
      </view>
      <view class="le-gird-line" :style="[lineStyle('power')]">
        <template v-if="showCircle('power')">
          <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToGird"
            v-if="flowData.power < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToGird"
            v-else-if="flowData.power > 1"></u-icon>
        </template>
      </view>
    </view>
    <view class="item-ce">
      <image src="../../../static/flow_hmi.png"></image>
    </view>
    <view class="item-ri">
      <view class="ri-bus flex u-flex-column align-items">
        <image src="../../../static/flow_dc.png" mode=""></image>
        <view class="u-m-r-10">{{ flowData.bus }} kW</view>
      </view>
      <view class="ri-bus-line" :style="[lineStyle('bus')]">
        <template v-if="showCircle('bus')">
         <u-icon name="play-right-fill" size="20rpx" color="#fb560a" class="rightToBus"
            v-if="flowData.bus < -1"></u-icon>
          <u-icon name="play-left-fill" size="20rpx" color="#fb560a" class="leftToBus"
            v-else-if="flowData.bus > 1"></u-icon>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    computed
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'

  const monitorStore = useMonitorStore()

  const deviceType = computed(() => monitorStore.routeQuery.type)
  const flowData = computed(() => {
    return monitorStore.flowData
  })
  const lineStyle = computed(() => (type) => {
    let color = showCircle.value(type) ? '#fb560a' : '#eeeeef'
    let backgroundColor = color
    return {
      backgroundColor
    }
  })
  // 流动拓补图展示圆
  const showCircle = computed(() => {
    let control = monitorStore.control
    let flowData = monitorStore.flowData
    return (name) => {
      if (!control) return false
      if (control.onLineState == '离线') return false
      if (-1 < flowData[name] && flowData[name] < 1) {
        return false
      } else if (flowData[name] == 0) {
        return false
      } else {
        return true
      }
    }
  })

  monitorStore.selectDynamicGraphFn({
    deviceSerialNumber: monitorStore.routeQuery.id,
    deviceType: monitorStore.routeQuery.type
  })
</script>

<style scoped lang="scss">
  image {
    width: 100%;
    height: 100%;
  }

  .flow-item {
    position: relative;
    height: 250rpx;
    font-size: 10px;

    .item-ce {
      width: 70rpx;
      height: 50rpx;
      z-index: 100;
      margin-top: 24rpx;
    }

    .item-le {
      position: relative;
      right: -18rpx;
      bottom: 50rpx;

      .le-gird {
        position: relative;
        top: 68rpx;
        left: -116rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .le-gird-line {
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;
        position: relative;

        .rightToGird {
          position: absolute;
          animation: rightToGird 3s linear infinite;
        }

        .leftToGird {
          position: absolute;
          animation: leftToGird 3s linear infinite;
        }

        @keyframes rightToGird {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }

        @keyframes leftToGird {
          100% {
            top: -7rpx;
            left: -12rpx;
          }

          0% {
            top: -7rpx;
            left: calc(100% - 12rpx);
          }
        }
      }
    }

    .item-ri {
      position: relative;
      left: -18rpx;
      bottom: 50rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .ri-bus {
        z-index: 0;
        position: relative;
        right: -70rpx;
        top: 68rpx;
        z-index: 100;

        image {
          width: 80rpx;
          height: 100rpx;
        }
      }

      .ri-bus-line {
        position: relative;
        height: 4rpx;
        width: 230rpx;
        background-color: #fb560a;

        .rightToBus {
          position: absolute;
          animation: rightToBus 3s linear infinite;
        }

        .leftToBus {
          position: absolute;
          animation: leftToBus 3s linear infinite;
        }

        @keyframes rightToBus {
          0% {
            top: -7rpx;
            left: -12rpx;
          }

          100% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
        }

        @keyframes leftToBus {
          0% {
            top: -7rpx;
            left: calc(100% - 14rpx);
          }
          
          100% {
            top: -7rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
</style>