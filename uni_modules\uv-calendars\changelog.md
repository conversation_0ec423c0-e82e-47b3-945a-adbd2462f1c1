## 1.0.15（2023-11-08）
1. 增加readonly属性，是否为只读状态，只读状态下禁止选择日期
## 1.0.14（2023-10-12）
1. 修复selected没有设置了info或者info设置为空字符串后，文本则无法恢复BUG
## 1.0.13（2023-09-19）
1. 修复range模式下，selected设置了info后选中后，导致文本不恢复的问题
2. 修复multiple模式下，selected自定义信息的颜色没变，依然是白色
## 1.0.12（2023-09-14）
1. 优化
## 1.0.11（2023-09-14）
1. 增加allowSameDay参数，是否允许日期范围的起止时间为同一天，mode = range时有效
2. 修复在vue2+小程序渲染时闪烁的问题
## 1.0.10（2023-09-07）
1. 修复国际化失效的BUG
## 1.0.9（2023-09-01）
1. 修复在pages.json中设置easycom会报错的BUG
## 1.0.8（2023-08-29）
1. 修复mainjs中设置setConfig修改属性不生效的问题，出自评论区：https://ext.dcloud.net.cn/plugin?id=12287
## 1.0.7（2023-08-26）
1. 去除range参数，由mode="range"替换
2. 新增mode参数，不传 / multiple / range，分别为单日期， 多个日期，选择日期范围
3. 与uv-calendar选择日期的功能保持一致
## 1.0.6（2023-08-25）
1. 修复点击返回今天按钮时，monthSwitch方法回调参数返回月份不是当天对应月份：https://github.com/climblee/uv-ui/issues/7
## 1.0.5（2023-08-13）
1. 修复选择月份弹窗层级的问题
## 1.0.4（2023-08-06）
1. 优化
## 1.0.3（2023-08-06）
1. 修复高度不对的BUG
2. 修复文案在小屏幕的BUG
## 1.0.2（2023-08-05）
1. 增加startText参数
2. 增加endText参数
3. 增加selected中的参数
4. 优化日历范围选择
## 1.0.1（2023-08-04）
1. 修复 自定义主题时 颜色错误的BUG
## 1.0.0（2023-08-03）
1. 新增 uv-calendars 新版日历发布
