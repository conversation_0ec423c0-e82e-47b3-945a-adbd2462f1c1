<template>
  <view class="scheme-list">
    <u-navbar
      :title="$t('versionFileList')"
      leftIconSize="30"
      :autoBack="true"
      :placeholder="true"
      class="tabbar"
      :titleStyle="{
        color: '#000'
      }"
    >
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx" @click="handleCconfirmClick">{{ $t('完成') }}</u-button>
      </template>
    </u-navbar>

    <view class="scheme-list-wrap u-p-t-10 u-p-b-10 u-m-b-20">
      <u-search
        v-model="searchValue"
        :clearabled="true"
        searchIconSize="30rpx"
        height="60rpx"
        :placeholder="placeholder"
        @search="handleSearchClick"
        @custom="handleSearchClick"
        :actionText="$t('搜索')"
      ></u-search>
    </view>

    <view class="scheme-list-wrap" v-if="initData.type == 'ini'">
      <view class="scheme-list-wrap-item flex" v-for="item in fileOptions" :key="item.bmsFilePathIni" @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ actived: item.bmsFilePathIni == initData.fileAllPathIni }">
          {{ item.bmsFileNameIni }}
        </view>
        <view v-if="item.bmsFilePathIni == initData.fileAllPathIni">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view class="scheme-list-wrap">
      <view class="scheme-list-wrap-item flex" v-for="item in fileOptions" :key="item.filePath" @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ actived: item.filePath == initData.fileAllPath }">
          {{ item.fileName }}
        </view>
        <view v-if="item.filePath == initData.fileAllPath">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>

    <view v-if="fileOptions.length == 0" style="height: calc(100% - 140rpx - 44px); background: #fff; padding-top: 300rpx; border-radius: 6px">
      <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')"></u-empty>
    </view>
  </view>
</template>

<script setup>
import { ref, toRefs } from 'vue';
import { onLoad, onShow, onUnload } from '@dcloudio/uni-app';
import { useParamStore } from '@/store/param';

const { ossOptions } = toRefs(useParamStore());
const initData = ref();
onLoad((options) => {
  initData.value = {
    ...options
  };
});
const fileOptions = ref([]);
onShow(() => {
  useParamStore()
    .allOssListFn({
      endpoint: initData.value.endpoint,
      fileType: initData.value.fileType,
      upgradeObject: initData.value.upgradeObject == 'null' ? undefined : initData.value.upgradeObject
    })
    .then(() => (fileOptions.value = ossOptions.value));
});

const selectData = ref();
const handleItemClick = (item) => {
  if (initData.value.type == 'ini') {
    initData.value.fileAllPathIni = item.bmsFilePathIni;
  } else {
    initData.value.fileAllPath = item.filePath;
  }
  selectData.value = item;
};
const handleCconfirmClick = () => {
  uni.$emit('fileItemClick', selectData.value, initData.value.type);
  uni.navigateBack({
    delta: 1
  });
};

/**
 * 搜索
 */
const searchValue = ref();
const placeholder = ref(uni.$t('请输入关键字'));
const handleSearchClick = () => {
  fileOptions.value = ossOptions.value.filter((item) => item[initData.value == 'ini' ? 'bmsFileNameIni' : 'fileName'].indexOf(searchValue.value) !== -1);
};
</script>

<style lang="scss" scoped>
.scheme-list {
  height: 100vh;
  overflow-y: auto;
  padding: 20rpx 30rpx;

  &-wrap {
    background-color: #fff;
    border-radius: 6px;
    padding: 0 30rpx;

    .actived {
      font-weight: bold;
      color: $u-primary;
    }

    &-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;
      justify-content: space-between;

      &-left {
        width: 80%;
      }
    }

    &-item:last-child {
      border-bottom: none;
    }
  }
}
</style>
