// 查询设备告警列表
export const alarmList = (queryInfo) => uni.$u.http.get('/system/alarm/list', {
  params: queryInfo,
  custom: {
    loading: false
  }
})

// 修改告警处理状态
export const updateStateByRecordNameIds = (data) => uni.$u.http.post('/system/alarm/updateStateByRecordNameIds', data)

// 获取详情
export const getAlarmInfo = (queryInfo) => uni.$u.http.get(`/system/alarm/${queryInfo.alarmNameId}`)

// 根据设备det、告警点、告警bit位、告警点位协议获取详细信息
export const getAlarmDetails = (data) => uni.$u.http.post('/system/method/getInfo', data)