<template>
  <view style="height: 100vh;padding: 20rpx 30rpx;overflow-y: auto;">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx;height: 30rpx;"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx;height: 35rpx;"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="setting1930" :label="$t('SOC上限设置')" required>
          <u-number-box v-model="form.setting1930" :min="5" :max="100" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1931" :label="$t('SOC下限设置')" required>
          <u-number-box v-model="form.setting1931" :min="0" :max="95" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1932" :label="$t('充电限流值')" required>
          <u-number-box v-model="form.setting1932" :step="0.1" :min="0" :max="2000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1933" :label="$t('放电限流值')" required>
          <u-number-box v-model="form.setting1933" :step="0.1" :min="0" :max="2000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1934" :label="$t('欠压保护')" required>
          <u-number-box v-model="form.setting1934" :step="0.1" :min="50" :max="1000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1935" :label="$t('欠压恢复')" required>
          <u-number-box v-model="form.setting1935" :step="0.1" :min="50" :max="1000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1936" :label="$t('过压保护')" required>
          <u-number-box v-model="form.setting1936" :step="0.1" :min="50" :max="1000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
        <u-form-item prop="setting1937" :label="$t('过压恢复')" required>
          <u-number-box v-model="form.setting1937" :step="0.1" :min="50" :max="1000" decimal-length="1" v-bind="inputNumberStyle"></u-number-box>
        </u-form-item>
      </u-form>
    </view>
    <view
      style="margin-top: 20rpx;font-size: 12px;padding: 20rpx 30rpx;background-color: #fff;border-radius: 6px;color: #b7b7b7;">
      <view style="margin-bottom: 10rpx;">
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('下发状态') }}：
        </view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('SOC上限设置') }}：
        </view>
        <view>{{ $t('设置电池停止充电时SOC。') }}（5~100）%</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('SOC下限设置') }}：
        </view>
        <view>{{ $t('设置电池停止放电时SOC。') }}（0~95）%</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('充电限流值') }}：
        </view>
        <view>{{ $t('设置电池充电时的电流最大值。') }}（0~2000）A</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('放电限流值') }}：
        </view>
        <view>{{ $t('设置电池放电时的电流最大值。') }}（0~2000）A</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('欠压保护') }}：
        </view>
        <view>{{ $t('电池放电保护电压。') }}（50~1000）V</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('欠压恢复') }}：
        </view>
        <view>{{ $t('电池可放电恢复电压。') }}（50~1000）V</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('过压保护') }}：
        </view>
        <view>{{ $t('电池充电电保护电压。') }}（50~1000）V</view>
      </view>
      <view>
        <view style="color: #333;margin-bottom: 10rpx;">
          {{ $t('过压恢复') }}：
        </view>
        <view>{{ $t('电池可充电恢复电压。') }}（50~1000）V</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx;border-radius: 6px;" @click="handleSendClick"
      :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey"
      style="width: 100%;display: flex; align-items: center;justify-content: center;" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx;margin-top: 1rpx;"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    reactive,
    getCurrentInstance,
    computed,
    toRefs
  } from 'vue';
  import {
    useParamStore
  } from '@/store/param.js'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    languageArr
  } from '@/locale/index.js'
  import {
    initService
  } from '@/common/request'
  import {
    onShow,
    onLoad
  } from '@dcloudio/uni-app'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { paramLabelStyle, inputNumberStyle } from '@/constant'

  const paramStore = useParamStore()
  const {
    routeQuery,
    control,
    groupControl
  } = toRefs(useMonitorStore())
  const {
    proxy
  } = getCurrentInstance()
  const isGroup = computed(() => isGroupFn(routeQuery.value.type))
  const current = ref()
  onLoad((options) => {
    current.value = options.index
  })

  // 使用 reactive 创建响应式状态  
  const form = ref({
    setting1930: undefined,
    setting1931: undefined,
    setting1932: undefined,
    setting1933: undefined,
    setting1934: undefined,
    setting1935: undefined,
    setting1936: undefined,
    setting1937: undefined,
    status: 0
  })
  const rules = ref({
    // setting1915: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请选择'),
    //   trigger: ['blur', 'change'],
    // },
    // setting1912: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
    // setting1913: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
    // setting1914: {
    //   type: 'string',
    //   required: true,
    //   message: uni.$t('请输入'),
    //   trigger: ['blur', 'change'],
    // },
  })
  const formRef = ref(null);
  const handleSendClick = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    // if (formRef.value) {
    //   formRef.value.validate().then(async valid => {
    //     if (valid) {
          try {
            await paramStore.sendParamBMSFn({
              setting1930: form.value.setting1930,
              setting1931: form.value.setting1931,
              setting1932: form.value.setting1932,
              setting1933: form.value.setting1933,
              setting1934: form.value.setting1934,
              setting1935: form.value.setting1935,
              setting1936: form.value.setting1936,
              setting1937: form.value.setting1937,
              ac: ac,
              id: form.value.id
            })
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发成功'))
            }, 20)
            getInfo()
          } catch (e) {
            setTimeout(() => {
              uni.$u.toast(uni.$t('下发失败'))
            }, 20)
            getInfo()
          }
      //   }
      // })
    // }
  }

  const isSend = computed(() => {
    if (isGroup.value) {
      let ac = routeQuery.value.groupId[current.value]
      let value = groupControl.value.find(item => item.ac == ac)
      if (!Object.keys(value).length) return true
      return value['onLineState'] == '离线'
    } else {
      if (!Object.keys(control.value).length) return true
      return control.value['onLineState'] == '离线'
    }
  })

  const getInfo = async () => {
    let ac = null
    if (isGroup.value) {
      ac = routeQuery.value.groupId[current.value]
    } else {
      ac = routeQuery.value.id
    }
    const res = await paramStore.bmsInfoFn({
      ac
    })
    if (!res) return
    for (let key in res) {
     if (res[key] == null) res[key] = undefined
    }
    let data = JSON.parse(JSON.stringify(form.value))
    // if (res.status == 1 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发成功'))
    // } else if (res.status == 3 && data.status == 2) {
    //   uni.$u.toast(uni.$t('下发失败'))
    // }
    form.value = {
      ...res,
    }
  }
  onShow(() => {
    getInfo()
  })
</script>

<style scoped lang="scss">
  .send-status {
    display: flex;
    align-items: center;

    image {
      margin-right: 6rpx;
    }
  }

  .param-form {
    width: 100%;
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
  }

  :deep(.u-form-item__body__right__content__slot) {
    flex-direction: row-reverse;
  }
</style>