<template>
  <view class="scheme-list">
    <u-navbar :title="$t('schemeList')" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;" @click="handleCconfirmClick">{{ $t('完成') }}</u-button>
      </template>
    </u-navbar>
    
    <view class="scheme-list-wrap u-p-t-10 u-p-b-10 u-m-b-20">
      <u-search v-model="searchValue" :clearabled="true" searchIconSize="30rpx" height="60rpx" :placeholder="placeholder" @search="handleSearchClick" @custom="handleSearchClick" :actionText="$t('搜索')"></u-search>
    </view>
    
    <template v-if="initData.type == 'jfpg'">
      <view class="scheme-list-wrap1" v-for="item in jfpgOptions" :key="item.id">
        <view class="scheme-list-wrap1-item flex" @click="handleItemClick(item)">
          <view class="scheme-list-wrap1-item-left flex" :class="{ 'actived': item.id == initData.id }">
            <u-icon name="list" :color="item.id == initData.id ? '#3c9cff': '#333'"></u-icon>
            <view class="u-line-1 ml-5">{{ item.title }}</view>
          </view>
          <view v-if="item.id == initData.id">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
        <view class="flex u-flex-between" @click="handleDetailsClick(item)">
          <view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建人员') }}：{{ item.createBy }}</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建时间') }}：{{ item.createTime }}</view>
            </view>
          </view>
          <u-icon name="arrow-right" color="#333"></u-icon>
        </view>
      </view>
    </template>
    <template v-if="initData.type == 'price'">
      <view class="scheme-list-wrap1" v-for="item in priceOptions" :key="item.id">
        <view class="scheme-list-wrap1-item flex" @click="handleItemClick(item)">
          <view class="scheme-list-wrap1-item-left flex" :class="{ 'actived': item.id == initData.id }">
            <u-icon name="list" :color="item.id == initData.id ? '#3c9cff': '#333'"></u-icon>
            <view class="u-line-1 ml-5">{{ item.name }}</view>
          </view>
          <view v-if="item.id == initData.id">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
        <view class="flex u-flex-between" @click="handleDetailsClick(item)">
          <view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建人员') }}：{{ item.createBy }}</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建时间') }}：{{ item.createTime }}</view>
            </view>
          </view>
          <u-icon name="arrow-right" color="#333"></u-icon>
        </view>
      </view>
    </template>
    <template v-if="initData.type == 'backup'">
      <view class="scheme-list-wrap1" v-for="item in backupOptions" :key="item.id">
        <view class="scheme-list-wrap1-item flex" @click="handleItemClick(item)">
          <view class="scheme-list-wrap1-item-left flex" :class="{ 'actived': item.id == initData.id }">
            <u-icon name="list" :color="item.id == initData.id ? '#3c9cff': '#333'"></u-icon>
            <view class="u-line-1 ml-5">{{ item.name }}</view>
          </view>
          <view v-if="item.id == initData.id">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
        <view class="flex u-flex-between" @click="handleDetailsClick(item)">
          <view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建人员') }}：{{ item.createBy }}</view>
            </view>
            <view class="scheme-list-wrap1-item1 flex">
              <view class="u-line-1">{{ $t('创建时间') }}：{{ item.createTime }}</view>
            </view>
          </view>
          <u-icon name="arrow-right" color="#333"></u-icon>
        </view>
      </view>
    </template>

    <!-- <view class="scheme-list-wrap">
      <template v-if="initData.type == 'jfpg'">
        <view class="scheme-list-wrap-item flex" v-for="item in jfpgOptions" :key="item.id" @click="handleItemClick(item)">
          <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.id }">{{ item.title }}</view>
          <view v-if="item.id == initData.id">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
      </template>
      <template v-if="initData.type == 'price'">
        <view class="scheme-list-wrap-item flex" v-for="item in priceOptions" :key="item.id" @click="handleItemClick(item)">
          <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.id }">{{ item.name }}</view>
          <view v-if="item.id == initData.id">
            <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
          </view>
        </view>
      </template>
      <template v-if="initData.type == 'backup'">
        <view class="scheme-list-wrap-item flex" v-for="item in backupOptions" :key="item.id" @click="handleItemClick(item)">
                  <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.id == initData.id }">{{ item.name }}</view>
                  <view v-if="item.id == initData.id">
                    <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
                  </view>
                </view>
      </template>
    </view> -->
    
    <view v-if="isShowEmpty" style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
    
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs
  } from 'vue'
  import {
    onLoad,
    onShow
  } from '@dcloudio/uni-app'
  import {
    useParamStore
  } from '@/store/param'

  const {
    jfpgOptions,
    priceOptions,
    backupOptions
  } = toRefs(useParamStore())
  const initData = ref()
  const isShowEmpty = ref(false)
  onLoad((options) => {
    initData.value = {
      ...options
    }
  })
  onShow(() => {
    if (initData.value.type == 'jfpg') {
      useParamStore().allJfpgFn().then(res => isShowEmpty.value = !jfpgOptions.value.length)
    } else if (initData.value.type == 'price') {
      useParamStore().allPriceFn().then(res => isShowEmpty.value = !priceOptions.value.length)
    } else if (initData.value.type == 'backup') {
      useParamStore().allBackUpFn().then(res => isShowEmpty.value = !backupOptions.value.length)
    }
  })
  
  const selectData = ref()
  const handleItemClick = (item) => {
    initData.value.id = item.id
    selectData.value = item
  }
  const handleCconfirmClick = () => {
    uni.$emit('schemeItemClick', selectData.value)
    uni.navigateBack({
      delta: 1
    })
  }
  const handleDetailsClick = (item) => {
    if (initData.value.type == 'jfpg') {
      uni.navigateTo({
        url: `/pages/operation/jfpg/jfpgDetails?id=${item.id}`
      })
    } else if (initData.value.type == 'price') {
      uni.navigateTo({
        url: `/pages/operation/price/priceDetails?id=${item.id}&type=${1}`
      })
    } else if (initData.value.type == 'backup') {
      uni.navigateTo({
        url: `/pages/operation/backup/backupDetails?id=${item.id}`
      })
    }
  }
  
  /**
   * 搜索
   */
  const searchValue = ref()
  const placeholder = ref(uni.$t('请输入关键字'))
  const handleSearchClick = () => {
    if (initData.value.type == 'jfpg') {
      useParamStore().allJfpgFn({
        title: searchValue.value
      }).then(res => isShowEmpty.value = !jfpgOptions.value.length)
    } else if (initData.value.type == 'price') {
      useParamStore().allPriceFn({
        name: searchValue.value
      }).then(res => isShowEmpty.value = !priceOptions.value.length)
    } else if (initData.value.type == 'backup') {
      useParamStore().allBackUpFn({
        name: searchValue.value
      }).then(res => isShowEmpty.value = !backupOptions.value.length)
    }
  }
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item1 {
        padding: 20rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>