# dw-picker-1
搜索“大王模板”查看全部页面模板
每天一款新模板，有需求请留言，优先安排需求较多的模板

# 使用说明
可以直接下载示例代码，里面有完整的示例
1. pages.json 中配置 easycom
```
{
  "easycom": {
    "^dw-(.*)": "dw-$1/components/dw-$1/dw-$1.vue",
    "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue"
  }
}
```

2. 页面中引用
```
// template
<view>
  <dw-picker-1 :keywords="keywords" :data="pickerOptions" @on-select="actionSelect"></dw-picker-1>
</view>
```

3. 注意事项（非常重要）
- 父元素必须设置 height 和 position: relative, 原因是右侧的导航按钮是绝对定位，会根据父元素的位置居中展示
- 该组件可以在页面中使用，也可以放到组件中使用如下

```
<u-popup>
  <dw-picker-1 :keywords="keywords" :data="pickerOptions" @on-select="actionSelect"></dw-picker-1>
</u-popup>
```

# 字段说明
|  字段   | 释义  |
|  ----  | ----  |
| keywords  | 筛选 label 等于 keywords 的所有数据，并且 keywords 高亮 |
| data  | picker选项，示例值参考下面的代码块 |

```
pickerOptions = {
  dataList: [
    {
      "label": "北京市",
      "value": "beijing",
      "initial": "B"
    },
    {
      "label": "上海市",
      "value": "shanghai",
      "initial": "S"
    },
    {
      "label": "广州市",
      "value": "guangzhou",
      "initial": "G"
    },
    {
      "label": "深圳市",
      "value": "shenzhen",
      "initial": "S"
    }
  ]
}
```



# 事件说明
|  字段   | 释义  |
|  ----  | ----  |
| on-select  | 选中选项时出发 |