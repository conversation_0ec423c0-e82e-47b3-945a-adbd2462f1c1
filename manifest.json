{
    "name" : "%app.name%",
    "appid" : "__UNI__655B477",
    "description" : "接入云平台，企业可以实现电力设备的实时监控和数据分析，提高管理效率，降低能源消耗和碳排放，优化电力设备的运行策略。",
    "versionName" : "1.1.3",
    "versionCode" : 138,
    "transformPx" : false,
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "optimization" : {
            "subPackages" : true
        },
        "flexible" : true,
        "screenOrientation" : [
            "portrait-primary",
            "portrait-secondary",
            "landscape-primary",
            "landscape-secondary"
        ],
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {},
        "distribute" : {
            "android" : {
                "packagename" : "uni.UNI655B477",
                "keystore" : "C:/Users/<USER>/Desktop/定制app/亿兰科云平台/secretKey/android/elecloud_app.keystore",
                "password" : "elecloud8888",
                "aliasname" : "elecloud_app",
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    // "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    // "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    // "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    // "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    // "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.GET_TASKS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    // "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    // "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    // "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    // "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    // "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>  ",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>  "
                ],
                "targetSdkVersion" : 30,
                "minSdkVersion" : 21
            },
            "ios" : {
                "dSYMs" : false,
                "idfa" : false,
                "urlScheme" : "myapp"
            },
            "sdkConfigs" : {
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "common",
                "iosStyle" : "common",
                "useOriginalMsgbox" : true
            },
            "orientation" : [
                "portrait-primary",
                "portrait-secondary",
                "landscape-primary",
                "landscape-secondary"
            ],
            "distribute" : {
                "screenOrientation" : [
                    "portrait-primary",
                    "portrait-secondary",
                    "landscape-primary",
                    "landscape-secondary"
                ],
                "distribute" : {
                    "screenOrientation" : [
                        "portrait-primary",
                        "portrait-secondary",
                        "landscape-primary",
                        "landscape-secondary"
                    ]
                }
            }
        },
        "locales" : {
            "en" : {
                "app.name" : "Elecloud"
            },
            "zh" : {
                "app.name" : "亿兰科云平台"
            },
            "it" : {
                "app.name" : "Elecloud"
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "locale" : "auto",
    "h5" : {
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "devServer" : {}
    },
    "fallbackLocale" : "zh-Hans",
    "distribute" : {
        "screenOrientation" : [ "portrait-primary", "landscape-primary" ]
    }
}
