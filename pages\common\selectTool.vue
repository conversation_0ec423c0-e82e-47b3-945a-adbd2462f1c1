<template>
  <view class="scheme-list">
    <u-navbar :title="title" leftIconSize="30" :autoBack="true" :placeholder="true" class="tabbar" :titleStyle="{
    		        color: '#000'
    		      }">
      <template #right>
        <u-button type="primary" size="mini" style="height: 50rpx;"
          @click="handleCconfirmClick">{{ $t('确定') }}</u-button>
      </template>
    </u-navbar>

    <view class="scheme-list-wrap" v-if="initData.actionName == 'lang'">
      <view class="scheme-list-wrap-item flex" v-for="item in languageArr" :key="item.value"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.value == initData.value }">
          {{ item.label }}
        </view>
        <view v-if="item.value == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view class="scheme-list-wrap" v-else-if="initData.actionName == 'service'">
      <view class="scheme-list-wrap-item flex" v-for="item in serviceArr" :key="item.value"
        @click="handleItemClick(item)">
        <view class="u-line-1 scheme-list-wrap-item-left" :class="{ 'actived': item.value == initData.value }">
          {{ item.label }}
        </view>
        <view v-if="item.value == initData.value">
          <u-icon name="checkbox-mark" color="#3c9cff"></u-icon>
        </view>
      </view>
    </view>
    <view v-if="!languageArr.length || !serviceArr.length"
      style="height: calc(100% - 140rpx - 44px);background: #fff;padding-top: 300rpx;border-radius: 6px;">
      <u-empty icon="../../static/empty.webp" width="300" height="300" textSize="14px" :text="$t('暂无数据')">
      </u-empty>
    </view>
  </view>
</template>

<script setup>
  import {
    ref,
    toRefs,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    onLoad,
    onShow,
    onUnload
  } from '@dcloudio/uni-app'
  import {
    languageArr,
    init as initLang
  } from '@/locale/index.js'
  import {
    initService
  } from '@/common/request'

  const {
    proxy
  } = getCurrentInstance()
  const initData = ref({})
  const title = ref(uni.$t('选择语言'))
  onLoad((options) => {
    initData.value = {
      ...options
    }
    if (initData.value.actionName == 'lang') {
      title.value = uni.$t('选择语言')
      initData.value.value = languageArr.find(item => item.value == uni.cache.getItem('language'))?.value
    } else {
      title.value = uni.$t('选择服务器')
      initData.value.value = serviceArr.value.find(item => item.value == uni.cache.getItem('service'))?.value
    }
  })

  const selectData = ref()
  const handleItemClick = (item) => {
    initData.value.value = item.value
    selectData.value = item
  }
  const handleCconfirmClick = () => {
    if (!initData.value.value) {
      let msg =  initData.value.actionName == 'lang' ? '请选择语言': '请选择服务器'
      uni.$u.toast(uni.$t(msg))
      return
    }
    if (!selectData.value) return uni.navigateBack()
    if (initData.value.actionName == 'lang') {
      uni.cache.setItem('language', selectData.value.value)
      proxy.$i18n.locale = selectData.value.value
      uni.setLocale(selectData.value.value)
      uni.$u.toast(uni.$t('切换语言成功'))
      uni.navigateBack()
    } else if (initData.value.actionName == 'service') {
      uni.cache.setItem('service', selectData.value.value)
      initService()
      uni.$u.toast(uni.$t('切换服务成功'))
      if (initData.value.type == 1) {
        uni.reLaunch({
          url: '/pages/common/login'
        })
      } else {
        uni.navigateBack()
      }
    }
    uni.$emit('toolClick', selectData.value)
  }

  const serviceArr = ref([])
  serviceArr.value = JSON.parse(import.meta.env.VITE_SERVICE_ARR).map(item => {
    if (item.value == 'zh' || item.value == 'en' || item.value == 'test') {
      item.label = uni.$t(item.label)
    }
    return item
  })
</script>

<style lang="scss" scoped>
  .scheme-list {
    height: 100vh;
    overflow-y: auto;
    padding: 20rpx 30rpx;

    &-wrap {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 30rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item:last-child {
        border-bottom: none;
      }
    }

    &-wrap1 {
      background-color: #fff;
      border-radius: 6px;
      padding: 0 30rpx 30rpx 30rpx;
      margin-bottom: 20rpx;

      .actived {
        font-weight: bold;
        color: $u-primary;
      }

      &-item {
        padding: 20rpx 0;
        border-bottom: 1px solid $uni-bg-color-grey;
        justify-content: space-between;

        &-left {
          width: 80%;
        }
      }

      &-item1 {
        padding: 10rpx 0 0 0;
        justify-content: space-between;
      }
    }
  }
</style>