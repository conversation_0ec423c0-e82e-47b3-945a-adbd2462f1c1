<template>
  <view class="data-time">
    <view>{{ $t('上报时间') }}</view>
    <view>{{ cp[0]?.sdt }}({{ baseInfo.timeZone }})</view>
  </view>
  <view class="device-info">
    <u-collapse :value="isExpand ? cpValue: []">
      <u-collapse-item v-for="(cpItem, index) in cp" :key="cpItem.dc" :name="cpItem.dc">
        <template #title>
          <view class="info-ti">{{ cpItem.name }}</view>
        </template>
        <template #right-icon>
          <view><u-icon name="play-right-fill" size="12px"></u-icon></view>
        </template>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('充电桩通信状态') }}</view>
          <view v-if="getStatus(index) == $t('离线')" style="color: #f56c6c;">{{ $t('离线') }}</view>
          <view v-else>{{ getStatus(index) }}</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('充电枪个数') }}</view>
          <view v-if="cpItem['chargingPile_19002']">{{ cpItem['chargingPile_19002'] }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('充电桩状态') }}</view>
          <view v-if="cpItem['chargingPile_19001']">{{ get19001(cpItem['chargingPile_19001']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('总输出功率') }}</view>
          <view v-if="cpItem['chargingPile_19003']">{{ cpItem['chargingPile_19003'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪输出电压') }}</view>
          <view v-if="cpItem['chargingPile_19004']">{{ cpItem['chargingPile_19004'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪输出电流') }}</view>
          <view v-if="cpItem['chargingPile_19005']">{{ cpItem['chargingPile_19005'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪输出功率') }}</view>
          <view v-if="cpItem['chargingPile_19006']">{{ cpItem['chargingPile_19006'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪当前SOC') }}</view>
          <view v-if="cpItem['chargingPile_19007']">{{ cpItem['chargingPile_19007'] }}<span class="item-unit">%</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪充电电量') }}</view>
          <view v-if="cpItem['chargingPile_19008']">{{ cpItem['chargingPile_19008'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪充电金额') }}</view>
          <view v-if="cpItem['chargingPile_19009']">{{ cpItem['chargingPile_19009'] }}<span class="item-unit">CNY</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪电池最高温度') }}</view>
          <view v-if="cpItem['chargingPile_19010']">{{ cpItem['chargingPile_19010'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪充电时长') }}</view>
          <view v-if="cpItem['chargingPile_19012']">{{ cpItem['chargingPile_19012'] }}<span class="item-unit">Min</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('1#枪状态') }}</view>
          <view v-if="cpItem['chargingPile_19011']">{{ get19011(cpItem['chargingPile_19011']) }}</view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪输出电压') }}</view>
          <view v-if="cpItem['chargingPile_19015']">{{ cpItem['chargingPile_19015'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪输出电流') }}</view>
          <view v-if="cpItem['chargingPile_19016']">{{ cpItem['chargingPile_19016'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪输出功率') }}</view>
          <view v-if="cpItem['chargingPile_19017']">{{ cpItem['chargingPile_19017'] }}<span class="item-unit">kW</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪当前SOC') }}</view>
          <view v-if="cpItem['chargingPile_19018']">{{ cpItem['chargingPile_19018'] }}<span class="item-unit">%</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪充电电量') }}</view>
          <view v-if="cpItem['chargingPile_19019']">{{ cpItem['chargingPile_19019'] }}<span class="item-unit">kWh</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪充电金额') }}</view>
          <view v-if="cpItem['chargingPile_19020']">{{ cpItem['chargingPile_19020'] }}<span class="item-unit">CNY</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪电池最高温度') }}</view>
          <view v-if="cpItem['chargingPile_19021']">{{ cpItem['chargingPile_19021'] }}<span class="item-unit">℃</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪充电时长') }}</view>
          <view v-if="cpItem['chargingPile_19023']">{{ cpItem['chargingPile_19023'] }}<span class="item-unit">Min</span></view>
          <view v-else>--</view>
        </view>
        <view class="info-item flex justify-content align-items">
          <view class="color-grey">{{ $t('2#枪状态') }}</view>
          <view v-if="cpItem['chargingPile_19022']">{{ get19011(cpItem['chargingPile_19022']) }}</view>
          <view v-else>--</view>
        </view>
      </u-collapse-item>
    </u-collapse>
  </view>
</template>

<script setup>
  import {
    ref,
    computed,
    getCurrentInstance
  } from 'vue'
  import {
    useMonitorStore
  } from '@/store/monitor.js'
  import {
    useLoginStore
  } from '@/store/login.js'
  import {
    get4058
  } from '@/common/parseBinaryToText.js'
  import otherColor from '../../../common/other.module.scss'
  import {
    isGroupFn
  } from '@/hook/useDeviceType.js'
  import { onShow } from '@dcloudio/uni-app'

  const isExpand = defineModel('isExpand', {
    default: true
  })
  const {
    proxy
  } = getCurrentInstance()
  const monitorStore = useMonitorStore()
  const userStore = useLoginStore()
  const baseInfo = computed(() => monitorStore.baseInfo)

  const cpValue = ref([])
  const cp = computed(() => {
    let data = monitorStore.pcs_cp
    cpValue.value = data.map(item => item.dc)
    let type = monitorStore.routeQuery.type
    let isGroup = isGroupFn(type)
    data.forEach(item => {
      if (isGroup) {
        item.name = `${item.label}_${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}`
      } else {
        item.name = `${parseInt(item.dc) - 191000 + 1}#${uni.$t('充电桩')}`
      }
    })
    return data
  })
  const getStatus = computed(() => {
    return (index) => {
      if (cp.value[index].isAnalysis == 0) {
        return cp.value.onLineState == '在线' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else if (cp.value[index].isAnalysis == 1) {
        return cp.value[index]['chargingPile_19000'] == '1' ? uni.$i18n().t('在线') : uni.$i18n().t('离线')
      } else {
        return '--'
      }
    }
  })
  const get19001 = computed(() => {
    return (num) => {
      switch (num) {
        case '0':
          return uni.$t('停机')
        case '1':
          return uni.$t('开机')
        case '2':
          return uni.$t('故障')
      }
    }
  })
  const get19011 = computed(() => {
    return (num) => getGunStatus(num)
  })

  const getGunStatus = (num) => {
    switch (num) {
      case '0':
        return uni.$t('空闲')
      case '1':
        return uni.$t('插枪')
      case '2':
        return uni.$t('充电等待')
      case '3':
        return uni.$t('启动中')
      case '4':
        return uni.$t('充电中')
      case '5':
        return uni.$t('重连')
      case '6':
        return '--'
      case '7':
        return uni.$t('结算状态')
      case '8':
        return uni.$t('故障状态')
      case '9':
        return uni.$t('放电中')
      case '10':
        return '--'
      case '11':
        return uni.$t('预约状态')
      case '12':
        return uni.$t('后台预约状态')
      case '13':
        return '--'
      case '14':
        return uni.$t('充电完成状态')
      case '15':
        return uni.$t('APP，预约状态')
      case '16':
        return uni.$t('试用期到，停止服务状态')
    }
  }
</script>

<style scoped lang="scss">
  .data-time {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    margin: 20rpx 30rpx;
    font-size: 10px;
    border-radius: 6px;
  }
  .device-info {
    /* background-color: $uni-bg-color; */
    background-color: #fff;
    border-radius: $uni-border-radius-lg;
    padding: 0 30rpx;
    margin: 20rpx 30rpx;

    .info-ti {
      font-weight: bold;
    }

    .info-item {
      padding: 30rpx 0;
      border-bottom: 1px solid $uni-bg-color-grey;

      .item-unit {
        font-size: 12px;
        color: $uni-text-color-grey;
        margin-left: 4rpx;
      }

      view:nth-child(2n) {
        width: 50%;
        text-align: right;
      }
    }

    .info-item:last-child {
      border-bottom: none;
    }
  }

  :deep(.u-collapse-item__content__text) {
    padding: 0;
    color: #000;
  }

  :deep(.u-cell__body) {
    padding: 30rpx 0;
  }

  :deep(.u-cell--clickable) {
    background-color: $uni-bg-color;
  }
</style>