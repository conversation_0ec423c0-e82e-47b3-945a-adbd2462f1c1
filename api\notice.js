// 获取未读公告列表
export const getUnreadNoticeList = (queryInfo) => uni.$u.http.get('/system/notice/listNoticeRecipients', {
  params: queryInfo,
  custom: {
    loading: false
  }
})

// 批量标记公告为已读
export const readNotice = (ids) => uni.$u.http.get(`/system/notice/editRead/${ids}`)

// 获取未读公告数量
export const getUnreadNoticeCount = () => uni.$u.http.get('/system/notice/selectByUnreadCount', {
  custom: {
    loading: false
  }
})

// 查询公告详细
export const getNotice = (noticeId) => uni.$u.http.get('/system/notice/' + noticeId, {
  custom: {
    loading: false
  }
})
