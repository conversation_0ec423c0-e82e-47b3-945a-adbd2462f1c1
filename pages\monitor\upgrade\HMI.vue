<template>
  <view style="height: 100vh; padding: 20rpx 30rpx; overflow-y: auto">
    <view class="param-form">
      <u-form labelPosition="left" labelWidth="auto" :model="form" :rules="rules" ref="formRef" :labelStyle="paramLabelStyle" errorType="toast">
        <u-form-item prop="status" :label="$t('下发状态')">
          <view class="send-status color-grey" v-if="form.status == 0">{{ $t('未下发') }}</view>
          <view class="send-status" v-if="form.status == 1">
            <image src="../../../static/green.png" mode="" style="width: 30rpx; height: 30rpx"></image>
            <view>{{ $t('下发成功') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 2">
            <image src="../../../static/yellow.png" mode="" style="width: 35rpx; height: 35rpx"></image>
            <view>{{ $t('下发中') }}</view>
          </view>
          <view class="send-status" v-if="form.status == 3">
            <image src="../../../static/red.png" mode="" style="width: 35rpx; height: 35rpx"></image>
            <view>{{ $t('下发失败') }}</view>
          </view>
        </u-form-item>
        <u-form-item prop="endpoint" :label="$t('访问域名')" required>
          <u-radio-group v-model="form.endpoint" @change="handleEndpointChange" placement="row" size="16px" style="display: flex; justify-content: flex-end">
            <u-radio v-for="(item, index) in addressOptions" :key="item.value" :label="item.label" :name="item.value" :class="[index == 0 && 'mr-20']"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="updateType" :label="$t('升级类型')" v-if="isShowV75051">
          <u-radio-group v-model="form.updateType" @change="handleUpdateTypeChange" placement="row" size="16px" style="display: flex; justify-content: flex-end">
            <u-radio :label="$t('主屏')" :name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('副屏')" :name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="fileType" :label="$t('文件类型')" @click="handleFileTypeClick">
          <view class="u-line-1" style="width: 60%; text-align: right" >
            {{ fileTypeText }}
          </view>
        </u-form-item>
        <u-form-item prop="failRestart" :label="$t('失败是否重启')" v-if="form.fileType == 2">
          <u-radio-group v-model="form.failRestart" placement="row" size="16px" style="display: flex; justify-content: flex-end">
            <u-radio :label="$t('是')" :name="1" class="mr-20"></u-radio>
            <u-radio :label="$t('否')" :name="0"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="upgradeObject" :label="$t('升级对象')" v-if="form.fileType == 3 || form.fileType == 4 || form.fileType == 5">
          <u-radio-group v-model="form.upgradeObject" placement="row" size="16px" style="display: flex; justify-content: flex-end">
            <u-radio v-for="item in objOptions" :key="item.value" :label="item.label" :name="item.value" class="mr-20"></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item prop="fileAllPath" :label="$t('版本文件')" required @click="handleCellClick()" v-if="form.fileType != 6">
          <view class="u-line-1" style="width: 60%; text-align: right">
            {{ versionText }}
          </view>
        </u-form-item>
        <u-form-item prop="fileAllPath" :label="$t('TAR版本文件')" required @click="handleCellClick()" v-if="form.fileType == 6">
          <view class="u-line-1" style="width: 60%; text-align: right">
            {{ versionText }}
          </view>
        </u-form-item>
        <u-form-item prop="fileAllPathIni" :label="$t('INI版本文件')" required @click="handleCellClick('ini')" v-if="form.fileType == 6">
          <view class="u-line-1" style="width: 60%; text-align: right">
            {{ versionBMSINIText }}
          </view>
        </u-form-item>
        <u-form-item :label="$t('升级进度')" v-if="form.status !== 0"></u-form-item>
        <view class="progress-info u-p-t-10 u-p-b-30" v-if="form.status !== 0">
          <view v-if="isShowProgress" style="line-height: 40px">
            <u-line-progress :percentage="percentage" :activeColor="progressColor" height="30"></u-line-progress>
            <view>{{ progressFormat(percentage) }}</view>
          </view>
          <view style="display: flex; align-items: center" v-else>
            <template v-if="form.fileType != 6">
              <template v-for="item in progressInfo">
                <view
                  :key="item.current"
                  v-if="!isEmpty(item)"
                  style="height: 120rpx; width: 100rpx; border: 1px solid #000; margin-right: 10rpx"
                  :style="{ background: item.background, borderColor: item.borderColor, color: item.color }"
                >
                  <view style="font-size: 12px; line-height: 80rpx; text-align: center; padding: 0 6rpx">
                    {{ $t('模块') }}{{ item.current }}
                    <u-line-progress :percentage="item.progress" :activeColor="colorsCom(item.status)" height="15"></u-line-progress>
                  </view>
                </view>
              </template>
              <view style="margin-left: 10rpx; font-size: 12px; color: #13ce66" v-if="form.status == 1">{{ $t('升级成功') }}</view>
              <view style="margin-left: 10rpx; font-size: 12px; color: #409eff" v-if="form.status == 2">{{ $t('升级中') }}</view>
              <view style="margin-left: 10rpx; font-size: 12px; color: #f56c6c" v-if="form.status == 3">{{ $t('升级失败') }}</view>
            </template>
            <template v-else>
              <view style="margin-left: 10rpx; font-size: 14px" :style="{ color: form.status == 1 ? '#13ce66' : '#f56c6c' }">{{ bmsUpdateMessage }}</view>
            </template>
          </view>
        </view>
      </u-form>
    </view>
    <view style="margin-top: 20rpx; font-size: 12px; padding: 20rpx 30rpx; background-color: #fff; border-radius: 6px; color: #b7b7b7">
      <view style="margin-bottom: 10rpx">
        <view style="color: #333; margin-bottom: 10rpx">{{ $t('下发状态') }}：</view>
        <view>{{ $t('未下发') }}：{{ $t('该类参数从未下发') }}</view>
        <view>{{ $t('下发中') }}：{{ $t('参数已成功下发至设备，执行未知，请等待') }}</view>
        <view>{{ $t('下发成功') }}：{{ $t('参数已成功下发至设备并已执行成功') }}</view>
        <view>{{ $t('下发失败') }}：{{ $t('参数已成功下发至设备，设备并未执行成功') }}</view>
      </view>
    </view>
    <u-button type="primary" style="margin-top: 40rpx; border-radius: 6px" @click="handleSendClick" :disabled="isSend">{{ $t('下发') }}</u-button>
    <view class="ft12 u-m-t-20 color-grey" style="width: 100%; display: flex; align-items: center; justify-content: center" v-if="isSend">
      <u-icon name="error-circle" size="14px" style="margin-right: 6rpx; margin-top: 1rpx"></u-icon>
      <view>
        {{ $t('设备已离线，不可下发') }}
      </view>
    </view>

    <u-picker
      :show="isShowSelect"
      :defaultIndex="defaultSelectIndex"
      keyName="label"
      :columns="[typeOptions]"
      itemHeight="88"
      @confirm="handleSelectConfirm"
      :closeOnClickOverlay="true"
      @close="handleSelectCancel"
      @cancel="handleSelectCancel"
      :cancelText="$t('取消')"
      :confirmText="$t('确认')"
    ></u-picker>
  </view>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, computed, toRefs, watch } from 'vue';
import { useParamStore } from '@/store/param.js';
import { useMonitorStore } from '@/store/monitor.js';
import { onShow, onLoad, onUnload } from '@dcloudio/uni-app';
import otherColor from '../../../common/other.module.scss';
import { isGroupFn } from '@/hook/useDeviceType.js';
import { isEmpty } from 'lodash';
import { paramLabelStyle, inputNumberStyle } from '@/constant';
import { isShowPerm } from '@/common/utils.js';

const paramStore = useParamStore();
const { routeQuery, control, groupControl, upgradeParmInfo } = toRefs(useMonitorStore());
const { proxy } = getCurrentInstance();
const isGroup = computed(() => isGroupFn(routeQuery.value.type));
const modelValue = defineModel({ type: Number });

// 使用 reactive 创建响应式状态
const form = ref({
  fileAllPath: null,
  status: 0,
  endpoint: 'http://oss-cn-shenzhen.aliyuncs.com',
  fileType: 2,
  upgradeObject: null,
  bucketname: null,
  uuid: null,
  failRestart: 0
});
const rules = ref({
  endpoint: {
    type: 'string',
    required: true,
    message: uni.$t('请选择'),
    trigger: ['blur', 'change']
  },
  fileAllPath: {
    type: 'string',
    required: true,
    message: uni.$t('请选择文件'),
    trigger: ['blur', 'change']
  }
});
const formRef = ref(null);
const addressOptions = ref([
  {
    value: 'http://oss-cn-shenzhen.aliyuncs.com',
    text: 1,
    label: uni.$t('深圳'),
    bucketname: 'elecod-oss'
  },
  {
    value: 'http://oss-ap-southeast-1.aliyuncs.com',
    text: 2,
    label: uni.$t('新加坡'),
    bucketname: 'elecod-oss-global'
  }
]);
const typeOptions = computed(() => {
  if (isShowV75051.value && form.value.updateType == 0)
    return [
      {
        value: 2,
        label: uni.$t('HMI升级文件')
      }
    ];
  if (isShowPerm(['system:sendMqtt:upgradeJson'])) {
    return [
      {
        value: 2,
        label: uni.$t('HMI升级文件')
      },
      {
        value: 3,
        label: uni.$t('MAC升级文件')
      },
      {
        value: 4,
        label: uni.$t('MDC升级文件')
      },
      {
        value: 5,
        label: uni.$t('STS升级文件')
      },
      {
        value: 6,
        label: uni.$t('BMS升级文件')
      }
    ];
  }
  if (isShowV74280.value && isShowPerm(['system:sendMqtt:BMSupgradeJson'])) {
    return [
      {
        value: 6,
        label: uni.$t('BMS升级文件')
      }
    ];
  }
  return [
    {
      value: 2,
      label: uni.$t('HMI升级文件')
    },
    {
      value: 3,
      label: uni.$t('MAC升级文件')
    },
    {
      value: 4,
      label: uni.$t('MDC升级文件')
    },
    {
      value: 5,
      label: uni.$t('STS升级文件')
    },
    {
      value: 6,
      label: uni.$t('BMS升级文件')
    }
  ];
});
const objOptions = computed(() => {
  let type = form.value.fileType;
  if (type == 3 || type == 4) {
    return [
      {
        value: 'DSP',
        label: 'DSP'
      },
      {
        value: 'ARM',
        label: 'ARM'
      }
    ];
  } else if (type == 5) {
    return [
      {
        value: 'DSP',
        label: 'DSP'
      }
    ];
  } else {
    return [];
  }
});
const handleSendClick = () => {
  let ac = null;
  if (isGroup.value) {
    ac = routeQuery.value.groupId[modelValue.value];
  } else {
    ac = routeQuery.value.id;
  }

  if (form.value.status == 2) return uni.$u.toast(uni.$t('正在下发中，请稍后再下发'));
  if (formRef.value) {
    formRef.value.validate().then(async (valid) => {
      if (valid) {
        try {
          await paramStore.sendParamUpdateFn({
            ac,
            fileAllPath: form.value.fileAllPath,
            endpoint: form.value.endpoint,
            fileType: form.value.fileType,
            upgradeObject: form.value.upgradeObject,
            version: form.value.version,
            bucketname: addressOptions.value.find((item) => item.value == form.value.endpoint).bucketname,
            updateType: isShowV75051.value ? form.value.updateType : null,
            failRestart: form.value.fileType == 2 ? form.value.failRestart : null,
            fileAllPathIni: form.value.fileType == 6 ? form.value.fileAllPathIni : null
          });
          setTimeout(() => {
            uni.$u.toast(uni.$t('下发成功'));
          }, 20);
          getInfo();
          clearInterval(progressTime.value);
        } catch (e) {
          console.log(e);
          setTimeout(() => {
            uni.$u.toast(uni.$t('下发失败'));
          }, 20);
          getInfo();
        }
      }
    });
  }
};

const isSend = computed(() => {
  if (isGroup.value) {
    let ac = routeQuery.value.groupId[modelValue.value];
    let value = groupControl.value.find((item) => item.ac == ac);
    if (!Object.keys(value).length) return true;
    return value['onLineState'] == '离线';
  } else {
    if (!Object.keys(control.value).length) return true;
    return control.value['onLineState'] == '离线';
  }
});

const getInfo = async () => {
  let ac = null;
  if (isGroup.value) {
    ac = routeQuery.value.groupId[modelValue.value];
  } else {
    ac = routeQuery.value.id;
  }
  const res = await paramStore.upgradeInfoFn({
    ac
  });
  if (!res) {
    if (isShowV74280.value && isShowPerm(['system:sendMqtt:BMSupgradeJson'])) form.value.fileType = 6;
    else form.value.fileType = 2;
    return;
  }
  form.value.status = res.status;
  form.value.uuid = res.uuid;
  form.value.fileType = res.fileType ? Number(res.fileType) : 2;
  form.value.fileAllPath = res.fileAllPath;
  versionText.value = res.fileAllPath;
  versionBMSINIText.value = res.fileAllPathIni;
  form.value.upgradeObject = res.upgradeObject;
  fileTypeText.value = typeOptions.value.find(item => item.value == res.fileType).label
  defaultSelectIndex.value = [res.fileType - 2]
  if (!form.value.uuid || form.value.status == 0) return;
  // 获取进度
  if (form.value.status == 2) {
    getProgress();
  } else {
    getProgressInfo();
  }
};

const progressTime = ref(null);
const getProgress = async () => {
  percentage.value = 0;
  await getInfoTwo();
  await getProgressInfo();
  let pollTime = 0;
  if (form.value.fileType == 2) {
    pollTime = 10000;
  } else {
    if (form.value.upgradeObject == 'DSP') {
      pollTime = progressInfo.value.length < 1 ? 10000 : 60000;
    } else if (form.value.upgradeObject == 'ARM') {
      pollTime = 10000;
    } else {
      pollTime = 60000;
    }
  }
  if (form.value.status !== 2) clearInterval(progressTime.value);
  progressValue.value = 0;
  progressTime.value = setInterval(() => {
    getInfoTwo().then(() => {
      getProgressInfo();
    });
  }, pollTime);
};
const getInfoTwo = async () => {
  let ac = null;
  if (isGroup.value) {
    ac = routeQuery.value.groupId[modelValue.value];
  } else {
    ac = routeQuery.value.id;
  }
  const res = await paramStore.upgradeInfoFn({
    ac
  });
  if (!res) return;
  form.value.status = res.status;
  form.value.uuid = res.uuid;
  form.value.fileType = res.fileType ? Number(res.fileType) : 2;
  form.value.fileAllPath = res.fileAllPath;
  versionText.value = res.fileAllPath;
  versionBMSINIText.value = res.fileAllPathIni;
  form.value.upgradeObject = res.upgradeObject;
  fileTypeText.value = typeOptions.value.find(item => item.value == res.fileType).label
  defaultSelectIndex.value = [res.fileType - 2]
  if (form.value.status !== 2) clearInterval(progressTime.value);
};
/**
 * 升级进度
 */
const colorsCom = computed(() => {
  return (status) => {
    if (status == 'success') {
      return '#67c23a';
    } else if (status == 'exception') {
      return '#f56c6c';
    } else {
      return '#409eff';
    }
  };
});
const percentage = ref(0);
const isDownloadFile = ref(true);
const isShowFail = ref(false);
watch(
  () => percentage.value,
  () => {
    if (percentage.value >= 100) {
      if (isDownloadFile.value) percentage.value = 99;
    }
  }
);
const progressFormat = computed(() => {
  return (percentage) => {
    if (form.value.status == 1) {
      if (form.value.fileType == 2) {
        return percentage === 100 ? `HMI ${uni.$t('升级成功')}` : `${percentage}%  ${uni.$t('正在下载远程升级包')}`;
      }
    } else {
      if (percentage === 100) {
        return uni.$t('远程升级包下载成功');
      } else {
        if (isShowFail.value && percentage == 0) return uni.$t('远程升级包下载失败');
        return `${percentage}%  ${uni.$t('正在下载远程升级包')}`;
      }
    }
  };
});
const progressInfo = ref([]);
const progressResInfo = ref([]);
const progressColor = ref('#409eff');
const isShowProgress = ref(true);
const progressValue = ref(0);
const bmsUpdateMessage = ref('');
watch(
  () => progressValue.value,
  () => {
    if (progressValue.value >= 100) {
      progressValue.value = 99;
    }
  }
);
const getProgressInfo = async () => {
  progressInfo.value = progressInfo.value.filter((item) => item?.mi == form.value.uuid || isEmpty(item));
  let ac = null;
  if (isGroup.value) {
    ac = routeQuery.value.groupId[modelValue.value];
  } else {
    ac = routeQuery.value.id;
  }
  const res = await paramStore.getUpgradeProgressFn({ ac, uuid: form.value.uuid });
  progressResInfo.value = res;
  if (!res.length) {
    // 返回为空数组时
    if (form.value.status == 3) {
      // 下发失败
      isDownloadFile.value = false;
      percentage.value = 0;
      progressColor.value = '#f56c6c';
      isShowProgress.value = true;
      isShowFail.value = true; // 是否显示失败
    } else {
      // 下发中
      isDownloadFile.value = true;
      percentage.value = percentage.value + 10;
      progressColor.value = '#409eff';
      isShowProgress.value = true;
      isShowFail.value = false; // 是否显示失败
    }
  } else if (res.length == 1) {
    // 下载文件成功，有模块的要加载模块
    isDownloadFile.value = false;
    isShowFail.value = false; // 是否显示失败
    if (res[0].dc == '12') {
      // 升级屏、只有下载文件
      if (form.value.status == 3) {
        isDownloadFile.value = false;
        percentage.value = 0;
        progressColor.value = '#f56c6c';
        isShowProgress.value = true;
        isShowFail.value = true; // 是否显示失败
      } else {
        percentage.value = 100;
        progressColor.value = '#67c23a';
        isShowProgress.value = true;
      }
    } else if (res[0].dc == '16') {
      if (res[0].type == '0' && res[0].softUpdResult == 'success') {
        percentage.value = 100;
        progressColor.value = '#67c23a';
        isShowProgress.value = true;
      } else {
        isDownloadFile.value = false;
        percentage.value = 0;
        progressColor.value = '#f56c6c';
        isShowProgress.value = true;
        isShowFail.value = true; // 是否显示失败
      }
    } else {
      percentage.value = 100;
      progressColor.value = '#67c23a';
      getProgressValue();
      // 第一个模块，自定义添加
      progressInfo.value = [
        {
          id: 0,
          mi: form.value.uuid,
          current: 1,
          total: 1,
          background: '#f4f4f5',
          borderColor: '#d3d4d6',
          color: '#909399',
          progress: progressValue,
          status: undefined
        }
      ];
      // 下载升级文件进度条隐藏
      isShowProgress.value = false;
    }
  } else {
    isDownloadFile.value = false;
    isShowProgress.value = false; // 下载升级文件进度条隐藏
    isShowFail.value = false; // 是否显示失败
    if (res.length == 2 && form.value.fileType == 6) {
      // 升级BMS
      let type = res[1].type;
      if (type == '10') {
        bmsUpdateMessage.value = uni.$t('升级成功');
      } else {
        bmsUpdateMessage.value = res[1].updDesc;
      }
    } else {
      setModuleFn(res);
    }
  }
};
const setModuleFn = (res) => {
  if (form.value.status == 2) {
    // 下发状态为--下发中
    res.forEach((item, index) => {
      if (index) {
        // 返回的报文必须要有数据
        if ((index = res.length - 1 && item.type == '2')) {
        } else {
          // 返回的数据的最后一项是否为下载完成的结果，如果是就不添加模块
          // 判断模块数组中是否包含返回的报文中的已经升级成功的模块数
          let currentIndex = progressInfo.value.findIndex((item1) => item1.id == Number(item.current));
          if (currentIndex == -1) {
            // 如果模块数组中没有包含返回的报文中的已经升级成功的模块数，则添加
            // 如果模块数组中没有数据，则直接添加
            if (progressInfo.value.length == 0) {
              // 返回的报文中最后一项为已升级成功的状态，则直接按照报文中的模块数添加至模块数组中
              if (res[res.length - 1].type == '2') {
                progressInfo.value[item.current] = {
                  id: Number(item.current),
                  mi: item.mi,
                  current: Number(item.current) + 1,
                  total: Number(item.total),
                  background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
                  borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
                  color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
                  progress: 100,
                  status: item.softUpdResult == 'error' ? 'exception' : 'success'
                };
                progressInfo.value = [...progressInfo.value];
              } else {
                // 返回的报文不一定包含几个模块，则自定义添加所包含的模块
                progressInfo.value.push({
                  id: Number(item.current),
                  mi: item.mi,
                  current: Number(item.current) + 1,
                  total: Number(item.total),
                  background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
                  borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
                  color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
                  progress: 100,
                  status: item.softUpdResult == 'error' ? 'exception' : 'success'
                });
                progressInfo.value = [...progressInfo.value];
                // 如果添加好的模块数小于模块总数，则添加下一个模块
                if (progressInfo.value.length < progressInfo.value[0].total) {
                  getProgressValue();
                  progressInfo.value.push({
                    id: progressInfo.value[progressInfo.value.length - 1].id + 1,
                    mi: progressInfo.value[0].mi,
                    current: progressInfo.value[progressInfo.value.length - 1].current + 1,
                    total: progressInfo.value[0].total,
                    background: '#f4f4f5',
                    borderColor: '#d3d4d6',
                    color: '#909399',
                    progress: progressValue.value >= 100 ? 99 : progressValue.value,
                    status: undefined
                  });
                  progressInfo.value = [...progressInfo.value];
                }
              }
            } else if (progressInfo.value.length !== 0 && progressInfo.value.length <= progressInfo.value[0].total) {
              getProgressValue();
              progressInfo.value.push({
                id: currentIndex + 1,
                mi: progressInfo.value[0].mi,
                current: progressInfo.value[0].current + 1,
                total: progressInfo.value[0].total,
                background: '#f4f4f5',
                borderColor: '#d3d4d6',
                color: '#909399',
                progress: progressValue.value >= 100 ? 99 : progressValue.value,
                status: undefined
              });
              progressInfo.value = [...progressInfo.value];
            }
          } else {
            // 模块数组中包含返回的报文中的已经升级成功的模块数 -- 已包含
            // 自定义的模块数据显示成功
            progressInfo.value[item.current] = {
              id: Number(item.current),
              mi: item.mi,
              current: Number(item.current) + 1,
              total: Number(item.total),
              background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
              borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
              color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
              progress: 100,
              status: item.softUpdResult == 'error' ? 'exception' : 'success'
            };
            progressInfo.value = [...progressInfo.value];
            // 如果模块数组的长度小于总模块数，则自定义添加一个模块
            if (progressInfo.value.length < progressInfo.value[0].total) {
              getProgressValue();
              progressInfo.value.push({
                id: progressInfo.value[progressInfo.value.length - 1].id + 1,
                mi: progressInfo.value[0].mi,
                current: progressInfo.value[progressInfo.value.length - 1].current + 1,
                total: progressInfo.value[0].total,
                background: '#f4f4f5',
                borderColor: '#d3d4d6',
                color: '#909399',
                progress: progressValue.value >= 100 ? 99 : progressValue.value,
                status: undefined
              });
              progressInfo.value = [...progressInfo.value];
            }
          }
        }
      }
    });
  } else {
    // 有下发结果，不管是失败还是成功
    res.forEach((item, index) => {
      if (index) {
        if ((index = res.length - 1 && item.type == '2')) {
        } else {
          progressInfo.value[item.current] = {
            id: Number(item.current),
            mi: item.mi,
            current: Number(item.current) + 1,
            total: Number(item.total),
            background: item.softUpdResult == 'error' ? '#fef0f0' : '#f0f9eb',
            borderColor: item.softUpdResult == 'error' ? '#fbc4c4' : '#c2e7b0',
            color: item.softUpdResult == 'error' ? '#f56c6c' : '#67c23a',
            progress: 100,
            status: item.softUpdResult == 'error' ? 'exception' : 'success'
          };
          progressInfo.value = [...progressInfo.value];
        }
      }
    });
  }
};

const getProgressValue = () => {
  if (form.value.fileType == 2) {
    progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 33;
  } else {
    if (form.value.upgradeObject == 'DSP') {
      progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 19;
    } else {
      progressValue.value = progressValue.value >= 100 ? 99 : progressValue.value + 33;
    }
  }
};

/**
 * 文件选择
 */
const handleEndpointChange = () => {
  versionText.value = '';
  form.value.fileAllPath = '';
};
const versionText = ref();
const versionBMSINIText = ref();
const handleCellClick = (type) => {
  let endpoint = addressOptions.value.find((item) => item.value == form.value.endpoint).text;
  uni.navigateTo({
    url: `/pages/monitor/upgrade/versionFileList?endpoint=${endpoint}&fileType=${form.value.fileType}&upgradeObject=${form.value.upgradeObject}&fileAllPath=${form.value.fileAllPath}&type=${type}&fileAllPathIni=${form.value.fileAllPathIni}`
  });
};
uni.$on('fileItemClick', (data, type) => {
  console.log(data)
  if (!data) return;
  if (type == 'ini') {
    versionBMSINIText.value = data.bmsFileNameIni;
    form.value.fileAllPathIni = data.bmsFilePathIni;
  } else {
    versionText.value = data.fileName;
    form.value.fileAllPath = data.filePath;
  }
  form.value.version = data.version;
  
  console.log(form.value)
});
onUnload(() => {
  uni.$off('fileItemClick');
});

/**
 * 文件类型选择
 */
const fileTypeText = ref();
const isShowSelect = ref(false);
const defaultSelectIndex = ref([0]);
const handleFileTypeClick = () => {
  isShowSelect.value = true
}
const handleSelectConfirm = ({ value }) => {
  if (value[0].value == 2) {
    form.value.upgradeObject = null
  }
  form.value.fileAllPath = null
  versionText.value = null
  versionBMSINIText.value = null
  form.value.fileAllPathIni = null
  
  form.value.fileType = value[0].value;
  fileTypeText.value = value[0].label
  isShowSelect.value = false;
};
const handleSelectCancel = () => {
  isShowSelect.value = false;
};

/**
 * 是否显示V75051
 * 芬兰主副屏
 * 主屏版本：jk_1107
 * 采集屏版本：jk_1000
 */
const isShowV75051 = computed(() => {
  if (!isGroup.value)
    if (isEmpty(control?.jk_1107)) return false;
    else if (isEmpty(groupControl[currentIndex]?.jk_1107)) return false;
  return true;
});
const handleUpdateTypeChange = () => {
  form.value.fileType = 2;
};

/**
 * 是否显示V74280
 * BMS升级
 */
const isShowV74280 = computed(() => {
  let versionStart = control.value?.jk_1000?.split('V')[1].split('.')[0];
  let versionTwo = control.value?.jk_1000?.split('V')[1].split('.')[1];
  let versionThere = control.value?.jk_1000?.split('V')[1].split('.')[2];
  if (versionStart == 7) if (versionTwo == 4280) return true;
  return false;
});

onLoad(() => {
  getInfo();
});
</script>

<style scoped lang="scss">
.send-status {
  display: flex;
  align-items: center;

  image {
    margin-right: 6rpx;
  }
}

.param-form {
  width: 100%;
  background-color: #fff;
  border-radius: $uni-border-radius-lg;
  padding: 0 30rpx;
}

:deep(.u-form-item__body__right__content__slot) {
  flex-direction: row-reverse;
}
</style>
