<template>
  <view class="zy-modal" :class="dshow?'show':''">
    <view class="zy-dialog" style="background-color: transparent;">
      <u-icon name="close" color="#909399" size="18px" class="icon-close" v-if="!forceupgrade" @click="upgrade_cancel"></u-icon>
      <view class="padding-top text-white u-p-t-60" :class="'zy-upgrade-topbg-'+theme">
        <u-image src="../../static/rocket.png" width="70px" height="70px" class="mb-20"></u-image>
        <view>
          <text class="zy-upgrade-title">
            {{ $t('发现新版本') }}
          </text>
        </view>
        <text class="flex-wrap u-m-t-15">V{{updated2version}}</text>
      </view>
      <view class="padding-xl bg-white text-center">
        <scroll-view style="max-height: 200rpx;" scroll-y="auto" v-if="!update_flag">
          <text>{{update_tips}}</text>
        </scroll-view>
        <view class="zy-progress radius striped active" v-if="update_flag">
          <view :class="'bg-'+theme" :style="'width: '+update_process+'%;'">
            {{update_process}}
          </view>
        </view>
      </view>
      <view class="zy-bar bg-white justify-center">
        <view class="action" v-if="!update_flag">
          <u-button type="primary" size="small" @click="upgrade_checked"
            style="width: auto;">{{ $t('确认升级') }}</u-button>
          <u-button size="small" v-if="!forceupgrade" @click="upgrade_cancel"
            style="width: auto;margin-left: 20rpx;">{{ $t('稍后再说') }}</u-button>
        </view>
        <view class="action text-center" v-if="update_flag&&!forceupgrade">
          <u-button type="primary" size="small" @click="upgrade_break"
            style="width: auto;">{{ $t('中断升级') }}</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'ZyUpgrade',
    props: {
      theme: { //主题，目前支持green,pink,blue,yellow,red
        type: String,
        default: 'green'
      },
      h5preview: { //H5界面下是否预览升级
        type: Boolean,
        default: false
      },
      oldversion: { //如果是H5，为了方便测试，可以传入一个旧版本号进来。
        type: String,
        default: ''
      },
      oldcode: { //如果是H5，为了方便测试，可以传一个旧版本的code进来。
        type: Number,
        default: 0
      },
      appstoreflag: { //是否启用appstore升级，如果启用，由服务端返回appstore的地址
        type: Boolean,
        default: false
      },
      noticeflag: { //是否通知主界面无需更新
        type: Boolean,
        default: false
      },
      autocheckupdate: { //是否页面截入时就判断升级
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        update_flag: false, //点击升级按钮后，显示进度条
        dshow: false,
        update_process: 0,
        downloadTask: [],
        updated2version: '',
        version_url: '',
        update_tips: '',
        forceupgrade: false,
        currentversion: this.oldversion,
        versionname: '',
        vesioncode: this.oldcode,
        wgt_flag: 0,
        wgt_url: '',
        size: 0, //开启gzip等情形下，获取不到安装包大小，可以服务端返回安装包大小
      }
    },
    mounted() {
      this.versionname = uni.$u.sys().appName
      let app_flag = false
      // #ifdef APP-PLUS
      app_flag = true
      // #endif
      if ((this.h5preview || app_flag) && this.autocheckupdate) {
        console.log("检测升级")
        this.check_update()
      }
    },
    computed: {
      version() {
        let retversion = ''
        retversion = this.currentversion + (this.currentversion != '' && this.updated2version != '' ? '->' : '') + this
          .updated2version
        return retversion
      }
    },
    methods: {
      //检测升级
      check_update() {
        let that = this
        // #ifdef APP-PLUS
        plus.runtime.getProperty(plus.runtime.appid, function(widgetInfo) {
          console.log(widgetInfo)
          that.currentversion = widgetInfo.version
          that.versionname = widgetInfo.name
          that.versioncode = widgetInfo.versionCode
          that.updatebusiness(that)
        });
        // #endif
        // #ifdef H5
        if (this.h5preview) {
          this.updatebusiness(that)
        }
        // #endif
        this.dshow = false
        uni.showTabBar()
      },
      updatebusiness: function(that) { //具体升级的业务逻辑
        uni.showLoading({
          title: '',
          mask: false
        });
        let platform = uni.getSystemInfoSync().platform
        let formdata = {
          method: "upgrade",
          version: that.currentversion,
          name: that.versionname,
          code: that.versioncode,
          ts: (new Date()).valueOf(),
          transid: '123',
          sign: '123',
          platform: platform
        }
        console.log('客户端请求参数：', formdata)
        uni.request({
          url: uni.$u.http.config.baseURL + 'system/appUpgrade/versionUpdate',
          method: 'POST',
          data: formdata,
          success: (result) => {
            console.log('服务器响应：', result)
            uni.hideLoading()
            // TODO 这里请根据服务端响应报文的具体格式进行调整
            let data = result.data
            if (data.code == 200) {
              // console.log('服务端响应数据：',data)
              //提示升级
              if (data.data.update_flag == 1) {
                that.dshow = true
                uni.hideTabBar()
                that.update_tips = data.data.update_tips
                that.forceupgrade = data.data.forceupdate == 1
                that.version_url = data.data.update_url
                //that.currentversion = widgetInfo.version
                that.updated2version = data.data.version
                that.wgt_flag = data.data.wgt_flag
                that.wgt_url = data.data.wgt_url
                that.size = data.data.size
              } else {
                if (that.noticeflag) {
                  //通知父组件，当前版为最新版本
                  that.$emit("showupdateTips", 0)
                }
              }
            } else {
              if (that.noticeflag) { 
                // uni.showToast({
                //   title: uni.$t('请求升级出错：') + data.msg,
                //   icon: 'none'
                // }); 
                console.log(data.msg)
                //通知父组件，当前版为最新版本
                that.$emit("showupdateTips", 0)
              }
            }
          },
          fail: (err) => {
            console.log('请求失败', err)
          }
        });
      },
      //点击开始升级按钮，开始升级
      upgrade_checked: function() {
        this.update_flag = true
        this.updateversion()
      },
      //点击取消升级按钮，取消升级
      upgrade_cancel: function() {
        this.dshow = false
        uni.showTabBar()
      },
      //升级过程中，点击中断升级按钮，中断升级
      upgrade_break: function() {
        this.downloadTask.abort()
        this.update_flag = false
      },
      //升级下载apk安装包的具体处理业务逻辑
      updateversion: function() {
        let platform = uni.getSystemInfoSync().platform
        let that = this
        console.log("操作系统：", platform)
        if (platform == 'ios' && this.appstoreflag) {
          //如果启用ios appstore升级，则打开appstore
          that.dshow = false
          uni.showTabBar()
          console.log("跳转至appstore")
          plus.runtime.launchApplication({
            action: that.version_url
          }, function(e) {
            uni.showToast({
              title: uni.$t('打开appstore失败'),
              icon: 'none'
            });
          });
        } else {
          let that = this
          let downloadurl = that.wgt_flag == 1 ? that.wgt_url : that.version_url
          this.update_confirm = true
          this.downloadTask = uni.downloadFile({
            url: `${uni.$u.http.config.baseURL}profile${downloadurl.split('profile')[1]}`,
            success: function(res) {
              console.log(res, downloadurl)
              if (res.statusCode == 200) {
                //开始安装
                plus.runtime.install(res.tempFilePath, {
                  force: false
                }, function() {
                  console.log('install success...');
                  plus.runtime.restart();
                }, function(e) {
                  console.error('install fail...', e);
                  uni.showToast({
                    title: uni.$t('升级失败'),
                    icon: 'none'
                  });
                  that.update_flag = false
                  that.upgrade_cancel()
                });
              } else {
                uni.showToast({
                  title: uni.$t('下载失败，网络错误'),
                  icon: 'none'
                });
                that.update_flag = false
                that.upgrade_cancel()
              }
            },
            fail: function(e) {
              console.log("下载失败", e)
              uni.showToast({
                title: uni.$t('下载失败') + ':' + e.errMsg,
                icon: 'none'
              });
              this.update_flag = false
            },
            complete: function() {}
          })
          this.downloadTask.onProgressUpdate(function(res) {
            that.update_process = res.progress
            if (res.progress == Infinity) {
              //使用size计算
              console.log("计算size");
              let progress = (res.totalBytesWritten / that.size) * 100
              if (progress > 100) {
                progress = 100
              }
              that.update_process = progress
            }
          })
        }
      },
    }
  }
</script>

<style scoped>
  @import url("@/static/main.css");

  .zy-upgrade-topbg-blue {
    /* background-image: url('@/static/images/blue.png'); */
    background-color: #fff;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 320rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
  }

  .zy-upgrade-title {
    font-size: 38rpx;
    font-weight: bold;
  }
  .icon-close {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
  }
</style>