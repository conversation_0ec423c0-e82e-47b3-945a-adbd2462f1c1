/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-06-06 10:54:40
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-01 10:01:19
 * @FilePath: \elecloud_mobile_copy\constant\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import otherColor from '../common/other.module.scss'

export const emTypeOptions = [{
    min: 100,
    max: 199,
    textKey: '计量点电表',
    type: '1'
  },
  {
    min: 200,
    max: 299,
    textKey: '储能电表',
    type: '2'
  },
  {
    min: 300,
    max: 399,
    textKey: 'PCC电表',
    type: '3'
  },
  {
    min: 400,
    max: 499,
    textKey: '光伏电表',
    type: '4'
  },
  {
    min: 500,
    max: 599,
    textKey: '负载电表',
    type: '5'
  },
  {
    min: 600,
    max: 699,
    textKey: '直流电表',
    type: '6'
  },
  {
    min: 700,
    max: 799,
    textKey: '市电电表',
    type: '7'
  }
]

/**
 * style constant
 */
export const formLabelStyle = {
  fontSize: '16px',
  color: otherColor.loginColor
}

export const paramLabelStyle = {
  fontSize: '14px'
}

export const newSearchCustomStyle = {
  backgroundColor: '#fff',
  borderRadius: '100px',
  height: '60rpx',
  borderColor: 'transparent',
}

export const inputNumberStyle = {
  buttonSize: '30px',
  inputWidth: '80px',
  color: '#ffffff',
  bgColor: otherColor.primaryColor,
  iconStyle: 'color: #fff',
  disabledInput: false
}